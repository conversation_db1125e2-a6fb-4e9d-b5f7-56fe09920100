package cp

import "sync"

var (
	cp1257     *charsetMap = nil
	cp1257Once sync.Once
)

func getcp1257() *charsetMap {
	cp1257Once.Do(func() {
		cp1257 = &charsetMap{
			sb: [256]rune{
				0x0000, //NULL
				0x0001, //START OF HEADING
				0x0002, //START OF TEXT
				0x0003, //END OF TEXT
				0x0004, //END OF TRANSMISSION
				0x0005, //ENQUIRY
				0x0006, //ACKNOWLEDGE
				0x0007, //BELL
				0x0008, //BACKSPACE
				0x0009, //HORIZONTAL TABULATION
				0x000A, //LINE FEED
				0x000B, //VERTICAL TABULATION
				0x000C, //FORM FEED
				0x000D, //CARRIAGE RETURN
				0x000E, //SHIFT OUT
				0x000F, //SHIFT IN
				0x0010, //DATA LINK ESCAPE
				0x0011, //DEVICE CONTROL ONE
				0x0012, //DEVICE CONTROL TWO
				0x0013, //DEVICE CONTROL THREE
				0x0014, //DEVICE CONTROL FOUR
				0x0015, //NEGATIVE ACKNOWLEDGE
				0x0016, //SYNCHRONOUS IDLE
				0x0017, //END OF TRANSMISSION BLOCK
				0x0018, //CANCEL
				0x0019, //END OF MEDIUM
				0x001A, //SUBSTITUTE
				0x001B, //ESCAPE
				0x001C, //FILE SEPARATOR
				0x001D, //GROUP SEPARATOR
				0x001E, //RECORD SEPARATOR
				0x001F, //UNIT SEPARATOR
				0x0020, //SPACE
				0x0021, //EXCLAMATION MARK
				0x0022, //QUOTATION MARK
				0x0023, //NUMBER SIGN
				0x0024, //DOLLAR SIGN
				0x0025, //PERCENT SIGN
				0x0026, //AMPERSAND
				0x0027, //APOSTROPHE
				0x0028, //LEFT PARENTHESIS
				0x0029, //RIGHT PARENTHESIS
				0x002A, //ASTERISK
				0x002B, //PLUS SIGN
				0x002C, //COMMA
				0x002D, //HYPHEN-MINUS
				0x002E, //FULL STOP
				0x002F, //SOLIDUS
				0x0030, //DIGIT ZERO
				0x0031, //DIGIT ONE
				0x0032, //DIGIT TWO
				0x0033, //DIGIT THREE
				0x0034, //DIGIT FOUR
				0x0035, //DIGIT FIVE
				0x0036, //DIGIT SIX
				0x0037, //DIGIT SEVEN
				0x0038, //DIGIT EIGHT
				0x0039, //DIGIT NINE
				0x003A, //COLON
				0x003B, //SEMICOLON
				0x003C, //LESS-THAN SIGN
				0x003D, //EQUALS SIGN
				0x003E, //GREATER-THAN SIGN
				0x003F, //QUESTION MARK
				0x0040, //COMMERCIAL AT
				0x0041, //LATIN CAPITAL LETTER A
				0x0042, //LATIN CAPITAL LETTER B
				0x0043, //LATIN CAPITAL LETTER C
				0x0044, //LATIN CAPITAL LETTER D
				0x0045, //LATIN CAPITAL LETTER E
				0x0046, //LATIN CAPITAL LETTER F
				0x0047, //LATIN CAPITAL LETTER G
				0x0048, //LATIN CAPITAL LETTER H
				0x0049, //LATIN CAPITAL LETTER I
				0x004A, //LATIN CAPITAL LETTER J
				0x004B, //LATIN CAPITAL LETTER K
				0x004C, //LATIN CAPITAL LETTER L
				0x004D, //LATIN CAPITAL LETTER M
				0x004E, //LATIN CAPITAL LETTER N
				0x004F, //LATIN CAPITAL LETTER O
				0x0050, //LATIN CAPITAL LETTER P
				0x0051, //LATIN CAPITAL LETTER Q
				0x0052, //LATIN CAPITAL LETTER R
				0x0053, //LATIN CAPITAL LETTER S
				0x0054, //LATIN CAPITAL LETTER T
				0x0055, //LATIN CAPITAL LETTER U
				0x0056, //LATIN CAPITAL LETTER V
				0x0057, //LATIN CAPITAL LETTER W
				0x0058, //LATIN CAPITAL LETTER X
				0x0059, //LATIN CAPITAL LETTER Y
				0x005A, //LATIN CAPITAL LETTER Z
				0x005B, //LEFT SQUARE BRACKET
				0x005C, //REVERSE SOLIDUS
				0x005D, //RIGHT SQUARE BRACKET
				0x005E, //CIRCUMFLEX ACCENT
				0x005F, //LOW LINE
				0x0060, //GRAVE ACCENT
				0x0061, //LATIN SMALL LETTER A
				0x0062, //LATIN SMALL LETTER B
				0x0063, //LATIN SMALL LETTER C
				0x0064, //LATIN SMALL LETTER D
				0x0065, //LATIN SMALL LETTER E
				0x0066, //LATIN SMALL LETTER F
				0x0067, //LATIN SMALL LETTER G
				0x0068, //LATIN SMALL LETTER H
				0x0069, //LATIN SMALL LETTER I
				0x006A, //LATIN SMALL LETTER J
				0x006B, //LATIN SMALL LETTER K
				0x006C, //LATIN SMALL LETTER L
				0x006D, //LATIN SMALL LETTER M
				0x006E, //LATIN SMALL LETTER N
				0x006F, //LATIN SMALL LETTER O
				0x0070, //LATIN SMALL LETTER P
				0x0071, //LATIN SMALL LETTER Q
				0x0072, //LATIN SMALL LETTER R
				0x0073, //LATIN SMALL LETTER S
				0x0074, //LATIN SMALL LETTER T
				0x0075, //LATIN SMALL LETTER U
				0x0076, //LATIN SMALL LETTER V
				0x0077, //LATIN SMALL LETTER W
				0x0078, //LATIN SMALL LETTER X
				0x0079, //LATIN SMALL LETTER Y
				0x007A, //LATIN SMALL LETTER Z
				0x007B, //LEFT CURLY BRACKET
				0x007C, //VERTICAL LINE
				0x007D, //RIGHT CURLY BRACKET
				0x007E, //TILDE
				0x007F, //DELETE
				0x20AC, //EURO SIGN
				0xFFFD, //UNDEFINED
				0x201A, //SINGLE LOW-9 QUOTATION MARK
				0xFFFD, //UNDEFINED
				0x201E, //DOUBLE LOW-9 QUOTATION MARK
				0x2026, //HORIZONTAL ELLIPSIS
				0x2020, //DAGGER
				0x2021, //DOUBLE DAGGER
				0xFFFD, //UNDEFINED
				0x2030, //PER MILLE SIGN
				0xFFFD, //UNDEFINED
				0x2039, //SINGLE LEFT-POINTING ANGLE QUOTATION MARK
				0xFFFD, //UNDEFINED
				0x00A8, //DIAERESIS
				0x02C7, //CARON
				0x00B8, //CEDILLA
				0xFFFD, //UNDEFINED
				0x2018, //LEFT SINGLE QUOTATION MARK
				0x2019, //RIGHT SINGLE QUOTATION MARK
				0x201C, //LEFT DOUBLE QUOTATION MARK
				0x201D, //RIGHT DOUBLE QUOTATION MARK
				0x2022, //BULLET
				0x2013, //EN DASH
				0x2014, //EM DASH
				0xFFFD, //UNDEFINED
				0x2122, //TRADE MARK SIGN
				0xFFFD, //UNDEFINED
				0x203A, //SINGLE RIGHT-POINTING ANGLE QUOTATION MARK
				0xFFFD, //UNDEFINED
				0x00AF, //MACRON
				0x02DB, //OGONEK
				0xFFFD, //UNDEFINED
				0x00A0, //NO-BREAK SPACE
				0xFFFD, //UNDEFINED
				0x00A2, //CENT SIGN
				0x00A3, //POUND SIGN
				0x00A4, //CURRENCY SIGN
				0xFFFD, //UNDEFINED
				0x00A6, //BROKEN BAR
				0x00A7, //SECTION SIGN
				0x00D8, //LATIN CAPITAL LETTER O WITH STROKE
				0x00A9, //COPYRIGHT SIGN
				0x0156, //LATIN CAPITAL LETTER R WITH CEDILLA
				0x00AB, //LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
				0x00AC, //NOT SIGN
				0x00AD, //SOFT HYPHEN
				0x00AE, //REGISTERED SIGN
				0x00C6, //LATIN CAPITAL LETTER AE
				0x00B0, //DEGREE SIGN
				0x00B1, //PLUS-MINUS SIGN
				0x00B2, //SUPERSCRIPT TWO
				0x00B3, //SUPERSCRIPT THREE
				0x00B4, //ACUTE ACCENT
				0x00B5, //MICRO SIGN
				0x00B6, //PILCROW SIGN
				0x00B7, //MIDDLE DOT
				0x00F8, //LATIN SMALL LETTER O WITH STROKE
				0x00B9, //SUPERSCRIPT ONE
				0x0157, //LATIN SMALL LETTER R WITH CEDILLA
				0x00BB, //RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
				0x00BC, //VULGAR FRACTION ONE QUARTER
				0x00BD, //VULGAR FRACTION ONE HALF
				0x00BE, //VULGAR FRACTION THREE QUARTERS
				0x00E6, //LATIN SMALL LETTER AE
				0x0104, //LATIN CAPITAL LETTER A WITH OGONEK
				0x012E, //LATIN CAPITAL LETTER I WITH OGONEK
				0x0100, //LATIN CAPITAL LETTER A WITH MACRON
				0x0106, //LATIN CAPITAL LETTER C WITH ACUTE
				0x00C4, //LATIN CAPITAL LETTER A WITH DIAERESIS
				0x00C5, //LATIN CAPITAL LETTER A WITH RING ABOVE
				0x0118, //LATIN CAPITAL LETTER E WITH OGONEK
				0x0112, //LATIN CAPITAL LETTER E WITH MACRON
				0x010C, //LATIN CAPITAL LETTER C WITH CARON
				0x00C9, //LATIN CAPITAL LETTER E WITH ACUTE
				0x0179, //LATIN CAPITAL LETTER Z WITH ACUTE
				0x0116, //LATIN CAPITAL LETTER E WITH DOT ABOVE
				0x0122, //LATIN CAPITAL LETTER G WITH CEDILLA
				0x0136, //LATIN CAPITAL LETTER K WITH CEDILLA
				0x012A, //LATIN CAPITAL LETTER I WITH MACRON
				0x013B, //LATIN CAPITAL LETTER L WITH CEDILLA
				0x0160, //LATIN CAPITAL LETTER S WITH CARON
				0x0143, //LATIN CAPITAL LETTER N WITH ACUTE
				0x0145, //LATIN CAPITAL LETTER N WITH CEDILLA
				0x00D3, //LATIN CAPITAL LETTER O WITH ACUTE
				0x014C, //LATIN CAPITAL LETTER O WITH MACRON
				0x00D5, //LATIN CAPITAL LETTER O WITH TILDE
				0x00D6, //LATIN CAPITAL LETTER O WITH DIAERESIS
				0x00D7, //MULTIPLICATION SIGN
				0x0172, //LATIN CAPITAL LETTER U WITH OGONEK
				0x0141, //LATIN CAPITAL LETTER L WITH STROKE
				0x015A, //LATIN CAPITAL LETTER S WITH ACUTE
				0x016A, //LATIN CAPITAL LETTER U WITH MACRON
				0x00DC, //LATIN CAPITAL LETTER U WITH DIAERESIS
				0x017B, //LATIN CAPITAL LETTER Z WITH DOT ABOVE
				0x017D, //LATIN CAPITAL LETTER Z WITH CARON
				0x00DF, //LATIN SMALL LETTER SHARP S
				0x0105, //LATIN SMALL LETTER A WITH OGONEK
				0x012F, //LATIN SMALL LETTER I WITH OGONEK
				0x0101, //LATIN SMALL LETTER A WITH MACRON
				0x0107, //LATIN SMALL LETTER C WITH ACUTE
				0x00E4, //LATIN SMALL LETTER A WITH DIAERESIS
				0x00E5, //LATIN SMALL LETTER A WITH RING ABOVE
				0x0119, //LATIN SMALL LETTER E WITH OGONEK
				0x0113, //LATIN SMALL LETTER E WITH MACRON
				0x010D, //LATIN SMALL LETTER C WITH CARON
				0x00E9, //LATIN SMALL LETTER E WITH ACUTE
				0x017A, //LATIN SMALL LETTER Z WITH ACUTE
				0x0117, //LATIN SMALL LETTER E WITH DOT ABOVE
				0x0123, //LATIN SMALL LETTER G WITH CEDILLA
				0x0137, //LATIN SMALL LETTER K WITH CEDILLA
				0x012B, //LATIN SMALL LETTER I WITH MACRON
				0x013C, //LATIN SMALL LETTER L WITH CEDILLA
				0x0161, //LATIN SMALL LETTER S WITH CARON
				0x0144, //LATIN SMALL LETTER N WITH ACUTE
				0x0146, //LATIN SMALL LETTER N WITH CEDILLA
				0x00F3, //LATIN SMALL LETTER O WITH ACUTE
				0x014D, //LATIN SMALL LETTER O WITH MACRON
				0x00F5, //LATIN SMALL LETTER O WITH TILDE
				0x00F6, //LATIN SMALL LETTER O WITH DIAERESIS
				0x00F7, //DIVISION SIGN
				0x0173, //LATIN SMALL LETTER U WITH OGONEK
				0x0142, //LATIN SMALL LETTER L WITH STROKE
				0x015B, //LATIN SMALL LETTER S WITH ACUTE
				0x016B, //LATIN SMALL LETTER U WITH MACRON
				0x00FC, //LATIN SMALL LETTER U WITH DIAERESIS
				0x017C, //LATIN SMALL LETTER Z WITH DOT ABOVE
				0x017E, //LATIN SMALL LETTER Z WITH CARON
				0x02D9, //DOT ABOVE
			},
		}
	})
	return cp1257
}
