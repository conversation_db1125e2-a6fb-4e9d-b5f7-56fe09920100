package x25519

import (
	fp "github.com/cloudflare/circl/math/fp25519"
)

// ladderJoye calculates a fixed-point multiplication with the generator point.
// The algorithm is the right-to-left <PERSON><PERSON>'s ladder as described
// in "How to precompute a ladder" in SAC'2017.
func ladderJoye(k *Key) {
	w := [5]fp.Elt{} // [mu,x1,z1,x2,z2] order must be preserved.
	fp.SetOne(&w[1]) // x1 = 1
	fp.SetOne(&w[2]) // z1 = 1
	w[3] = fp.Elt{   // x2 = G-S
		0xbd, 0xaa, 0x2f, 0xc8, 0xfe, 0xe1, 0x94, 0x7e,
		0xf8, 0xed, 0xb2, 0x14, 0xae, 0x95, 0xf0, 0xbb,
		0xe2, 0x48, 0x5d, 0x23, 0xb9, 0xa0, 0xc7, 0xad,
		0x34, 0xab, 0x7c, 0xe2, 0xee, 0xcd, 0xae, 0x1e,
	}
	fp.SetOne(&w[4]) // z2 = 1

	const n = 255
	const h = 3
	swap := uint(1)
	for s := 0; s < n-h; s++ {
		i := (s + h) / 8
		j := (s + h) % 8
		bit := uint((k[i] >> uint(j)) & 1)
		copy(w[0][:], tableGenerator[s*Size:(s+1)*Size])
		diffAdd(&w, swap^bit)
		swap = bit
	}
	for s := 0; s < h; s++ {
		double(&w[1], &w[2])
	}
	toAffine((*[fp.Size]byte)(k), &w[1], &w[2])
}

// ladderMontgomery calculates a generic scalar point multiplication
// The algorithm implemented is the left-to-right Montgomery's ladder.
func ladderMontgomery(k, xP *Key) {
	w := [5]fp.Elt{}      // [x1, x2, z2, x3, z3] order must be preserved.
	w[0] = *(*fp.Elt)(xP) // x1 = xP
	fp.SetOne(&w[1])      // x2 = 1
	w[3] = *(*fp.Elt)(xP) // x3 = xP
	fp.SetOne(&w[4])      // z3 = 1

	move := uint(0)
	for s := 255 - 1; s >= 0; s-- {
		i := s / 8
		j := s % 8
		bit := uint((k[i] >> uint(j)) & 1)
		ladderStep(&w, move^bit)
		move = bit
	}
	toAffine((*[fp.Size]byte)(k), &w[1], &w[2])
}

func toAffine(k *[fp.Size]byte, x, z *fp.Elt) {
	fp.Inv(z, z)
	fp.Mul(x, x, z)
	_ = fp.ToBytes(k[:], x)
}

var lowOrderPoints = [5]fp.Elt{
	{ /* (0,_,1) point of order 2 on Curve25519 */
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	},
	{ /* (1,_,1) point of order 4 on Curve25519 */
		0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	},
	{ /* (x,_,1) first point of order 8 on Curve25519 */
		0xe0, 0xeb, 0x7a, 0x7c, 0x3b, 0x41, 0xb8, 0xae,
		0x16, 0x56, 0xe3, 0xfa, 0xf1, 0x9f, 0xc4, 0x6a,
		0xda, 0x09, 0x8d, 0xeb, 0x9c, 0x32, 0xb1, 0xfd,
		0x86, 0x62, 0x05, 0x16, 0x5f, 0x49, 0xb8, 0x00,
	},
	{ /* (x,_,1) second point of order 8 on Curve25519 */
		0x5f, 0x9c, 0x95, 0xbc, 0xa3, 0x50, 0x8c, 0x24,
		0xb1, 0xd0, 0xb1, 0x55, 0x9c, 0x83, 0xef, 0x5b,
		0x04, 0x44, 0x5c, 0xc4, 0x58, 0x1c, 0x8e, 0x86,
		0xd8, 0x22, 0x4e, 0xdd, 0xd0, 0x9f, 0x11, 0x57,
	},
	{ /* (-1,_,1) a point of order 4 on the twist of Curve25519 */
		0xec, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
		0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
		0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
		0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f,
	},
}
