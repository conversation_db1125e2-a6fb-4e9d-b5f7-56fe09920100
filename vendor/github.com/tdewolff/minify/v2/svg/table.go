package svg

var colorAttrMap = map[Hash]bool{
	Color:          true,
	Fill:           true,
	Stroke:         true,
	Stop_Color:     true,
	Flood_Color:    true,
	Lighting_Color: true,
}

// var styleAttrMap = map[svg.Hash]bool{
// 	svg.Font:                         true,
// 	svg.Font_Family:                  true,
// 	svg.Font_Size:                    true,
// 	svg.Font_Size_Adjust:             true,
// 	svg.Font_Stretch:                 true,
// 	svg.Font_Style:                   true,
// 	svg.Font_Variant:                 true,
// 	svg.Font_Weight:                  true,
// 	svg.Direction:                    true,
// 	svg.Letter_Spacing:               true,
// 	svg.Text_Decoration:              true,
// 	svg.Unicode_Bidi:                 true,
// 	svg.White_Space:                  true,
// 	svg.Word_Spacing:                 true,
// 	svg.Clip:                         true,
// 	svg.Color:                        true,
// 	svg.Cursor:                       true,
// 	svg.Display:                      true,
// 	svg.Overflow:                     true,
// 	svg.Visibility:                   true,
// 	svg.Clip_Path:                    true,
// 	svg.Clip_Rule:                    true,
// 	svg.Mask:                         true,
// 	svg.Opacity:                      true,
// 	svg.Enable_Background:            true,
// 	svg.Filter:                       true,
// 	svg.Flood_Color:                  true,
// 	svg.Flood_Opacity:                true,
// 	svg.Lighting_Color:               true,
// 	svg.Solid_Color:                  true,
// 	svg.Solid_Opacity:                true,
// 	svg.Stop_Color:                   true,
// 	svg.Stop_Opacity:                 true,
// 	svg.Pointer_Events:               true,
// 	svg.Buffered_Rendering:           true,
// 	svg.Color_Interpolation:          true,
// 	svg.Color_Interpolation_Filters:  true,
// 	svg.Color_Profile:                true,
// 	svg.Color_Rendering:              true,
// 	svg.Fill:                         true,
// 	svg.Fill_Opacity:                 true,
// 	svg.Fill_Rule:                    true,
// 	svg.Image_Rendering:              true,
// 	svg.Marker:                       true,
// 	svg.Marker_End:                   true,
// 	svg.Marker_Mid:                   true,
// 	svg.Marker_Start:                 true,
// 	svg.Shape_Rendering:              true,
// 	svg.Stroke:                       true,
// 	svg.Stroke_Dasharray:             true,
// 	svg.Stroke_Dashoffset:            true,
// 	svg.Stroke_Linecap:               true,
// 	svg.Stroke_Linejoin:              true,
// 	svg.Stroke_Miterlimit:            true,
// 	svg.Stroke_Opacity:               true,
// 	svg.Stroke_Width:                 true,
// 	svg.Paint_Order:                  true,
// 	svg.Vector_Effect:                true,
// 	svg.Viewport_Fill:                true,
// 	svg.Viewport_Fill_Opacity:        true,
// 	svg.Text_Rendering:               true,
// 	svg.Alignment_Baseline:           true,
// 	svg.Baseline_Shift:               true,
// 	svg.Dominant_Baseline:            true,
// 	svg.Glyph_Orientation_Horizontal: true,
// 	svg.Glyph_Orientation_Vertical:   true,
// 	svg.Kerning:                      true,
// 	svg.Text_Anchor:                  true,
// 	svg.Writing_Mode:                 true,
// }
