package html

type traits uint16

const (
	normalTag traits = 1 << iota
	rawTag           // raw tags need special processing for their content
	blockTag         // remove spaces around these tags
	objectTag        // keep spaces after these open/close tags
	omitPTag         // omit p end tag if it is followed by this start tag
	keepPTag         // keep p end tag if it is followed by this end tag
)

const (
	booleanAttr traits = 1 << iota
	urlAttr
	trimAttr
)

var tagMap = map[Hash]traits{
	A:          keepPTag,
	Abbr:       normalTag,
	Address:    blockTag | omitPTag,
	Area:       normalTag,
	Article:    blockTag | omitPTag,
	Aside:      blockTag | omitPTag,
	Audio:      keepPTag,
	B:          normalTag,
	Base:       normalTag,
	Bb:         normalTag,
	Bdi:        normalTag,
	Bdo:        normalTag,
	Blockquote: blockTag | omitPTag,
	Body:       normalTag,
	Br:         blockTag,
	Button:     objectTag,
	Canvas:     objectTag | keepPTag,
	Caption:    blockTag,
	Cite:       normalTag,
	Code:       normalTag,
	Col:        blockTag,
	Colgroup:   blockTag,
	Data:       normalTag,
	Datalist:   normalTag, // no text content
	Dd:         blockTag,
	Del:        keepPTag,
	Details:    blockTag | omitPTag,
	Dfn:        normalTag,
	Dialog:     normalTag,
	Div:        blockTag | omitPTag,
	Dl:         blockTag | omitPTag,
	Dt:         blockTag,
	Em:         normalTag,
	Embed:      normalTag,
	Fieldset:   blockTag | omitPTag,
	Figcaption: blockTag | omitPTag,
	Figure:     blockTag | omitPTag,
	Footer:     blockTag | omitPTag,
	Form:       blockTag | omitPTag,
	H1:         blockTag | omitPTag,
	H2:         blockTag | omitPTag,
	H3:         blockTag | omitPTag,
	H4:         blockTag | omitPTag,
	H5:         blockTag | omitPTag,
	H6:         blockTag | omitPTag,
	Head:       blockTag,
	Header:     blockTag | omitPTag,
	Hgroup:     blockTag,
	Hr:         blockTag | omitPTag,
	Html:       blockTag,
	I:          normalTag,
	Iframe:     rawTag | objectTag,
	Img:        objectTag,
	Input:      objectTag,
	Ins:        keepPTag,
	Kbd:        normalTag,
	Label:      normalTag | keepPTag, // experimentally, keepPTag is needed
	Legend:     blockTag,
	Li:         blockTag,
	Link:       normalTag,
	Main:       blockTag | omitPTag,
	Map:        keepPTag,
	Mark:       normalTag,
	Math:       rawTag,
	Menu:       blockTag | omitPTag,
	Meta:       normalTag,
	Meter:      objectTag,
	Nav:        blockTag | omitPTag,
	Noscript:   blockTag | keepPTag,
	Object:     objectTag,
	Ol:         blockTag | omitPTag,
	Optgroup:   normalTag, // no text content
	Option:     blockTag,
	Output:     normalTag,
	P:          blockTag | omitPTag,
	Param:      normalTag,
	Picture:    normalTag,
	Pre:        blockTag | omitPTag,
	Progress:   objectTag,
	Q:          objectTag,
	Rp:         normalTag,
	Rt:         objectTag,
	Ruby:       normalTag,
	S:          normalTag,
	Samp:       normalTag,
	Script:     rawTag,
	Section:    blockTag | omitPTag,
	Select:     objectTag,
	Slot:       normalTag,
	Small:      normalTag,
	Source:     normalTag,
	Span:       normalTag,
	Strong:     normalTag,
	Style:      rawTag | blockTag,
	Sub:        normalTag,
	Summary:    blockTag,
	Sup:        normalTag,
	Svg:        rawTag | objectTag,
	Table:      blockTag | omitPTag,
	Tbody:      blockTag,
	Td:         blockTag,
	Template:   normalTag,
	Textarea:   rawTag | objectTag,
	Tfoot:      blockTag,
	Th:         blockTag,
	Thead:      blockTag,
	Time:       normalTag,
	Title:      blockTag,
	Tr:         blockTag,
	Track:      normalTag,
	U:          normalTag,
	Ul:         blockTag | omitPTag,
	Var:        normalTag,
	Video:      objectTag | keepPTag,
	Wbr:        objectTag,

	// removed tags
	Acronym:   normalTag,
	Applet:    normalTag,
	Basefont:  normalTag,
	Big:       normalTag,
	Center:    blockTag,
	Dir:       blockTag,
	Font:      normalTag,
	Frame:     normalTag,
	Frameset:  normalTag,
	Image:     objectTag,
	Marquee:   blockTag,
	Menuitem:  normalTag,
	Nobr:      normalTag,
	Noembed:   blockTag,
	Noframes:  blockTag,
	Plaintext: normalTag,
	Rtc:       objectTag,
	Rb:        normalTag,
	Strike:    normalTag,
	Tt:        normalTag,
	Xmp:       blockTag,

	// experimental tags
	Portal: normalTag,
}

var attrMap = map[Hash]traits{
	Accept:                   trimAttr, // list of mimetypes
	Accept_Charset:           trimAttr,
	Accesskey:                trimAttr,
	Action:                   urlAttr,
	Allow:                    trimAttr,
	Allowfullscreen:          booleanAttr,
	As:                       trimAttr,
	Async:                    booleanAttr,
	Autocapitalize:           trimAttr,
	Autocomplete:             trimAttr,
	Autofocus:                booleanAttr,
	Autoplay:                 booleanAttr,
	Blocking:                 trimAttr,
	Capture:                  trimAttr,
	Charset:                  trimAttr,
	Checked:                  booleanAttr,
	Cite:                     urlAttr,
	Class:                    trimAttr,
	Color:                    trimAttr,
	Cols:                     trimAttr, // uint bigger than 0
	Colspan:                  trimAttr, // uint bigger than 0
	Contenteditable:          trimAttr,
	Controls:                 booleanAttr,
	Coords:                   trimAttr, // list of floats
	Crossorigin:              trimAttr,
	Data:                     urlAttr,
	Datetime:                 trimAttr,
	Decoding:                 trimAttr,
	Default:                  booleanAttr,
	Defer:                    booleanAttr,
	Dir:                      trimAttr,
	Disabled:                 booleanAttr,
	Draggable:                trimAttr,
	Enctype:                  trimAttr, // mimetype
	Enterkeyhint:             trimAttr,
	Fetchpriority:            trimAttr,
	For:                      trimAttr,
	Form:                     trimAttr,
	Formaction:               urlAttr,
	Formenctype:              trimAttr, // mimetype
	Formmethod:               trimAttr,
	Formnovalidate:           booleanAttr,
	Formtarget:               trimAttr,
	Headers:                  trimAttr,
	Height:                   trimAttr, // uint
	Hidden:                   trimAttr, // TODO: boolean
	High:                     trimAttr, // float
	Href:                     urlAttr,
	Hreflang:                 trimAttr, // BCP 47
	Http_Equiv:               trimAttr,
	Imagesizes:               trimAttr,
	Imagesrcset:              trimAttr,
	Inert:                    booleanAttr,
	Inputmode:                trimAttr,
	Is:                       trimAttr,
	Ismap:                    booleanAttr,
	Itemid:                   urlAttr,
	Itemprop:                 trimAttr,
	Itemref:                  trimAttr,
	Itemscope:                booleanAttr,
	Itemtype:                 trimAttr, // list of urls
	Kind:                     trimAttr,
	Lang:                     trimAttr, // BCP 47
	List:                     trimAttr,
	Loading:                  trimAttr,
	Loop:                     booleanAttr,
	Low:                      trimAttr, // float
	Max:                      trimAttr, // float or varies
	Maxlength:                trimAttr, // uint
	Media:                    trimAttr,
	Method:                   trimAttr,
	Min:                      trimAttr, // float or varies
	Minlength:                trimAttr, // uint
	Multiple:                 booleanAttr,
	Muted:                    booleanAttr,
	Nomodule:                 booleanAttr,
	Novalidate:               booleanAttr,
	Open:                     booleanAttr,
	Optimum:                  trimAttr, // float
	Pattern:                  trimAttr, // regex
	Ping:                     trimAttr, // list of urls
	Playsinline:              booleanAttr,
	Popover:                  trimAttr,
	Popovertarget:            trimAttr,
	Popovertargetaction:      trimAttr,
	Poster:                   urlAttr,
	Preload:                  trimAttr,
	Profile:                  urlAttr,
	Readonly:                 booleanAttr,
	Referrerpolicy:           trimAttr,
	Rel:                      trimAttr,
	Required:                 booleanAttr,
	Reversed:                 booleanAttr,
	Rows:                     trimAttr, // uint bigger than 0
	Rowspan:                  trimAttr, // uint
	Sandbox:                  trimAttr,
	Scope:                    trimAttr,
	Selected:                 booleanAttr,
	Shadowrootmode:           trimAttr,
	Shadowrootdelegatesfocus: booleanAttr,
	Shape:                    trimAttr,
	Size:                     trimAttr, // uint bigger than 0
	Sizes:                    trimAttr,
	Span:                     trimAttr, // uint bigger than 0
	Spellcheck:               trimAttr,
	Src:                      urlAttr,
	Srclang:                  trimAttr, // BCP 47
	Srcset:                   trimAttr,
	Start:                    trimAttr, // int
	Step:                     trimAttr, // float or "any"
	Tabindex:                 trimAttr, // int
	Target:                   trimAttr,
	Translate:                trimAttr,
	Type:                     trimAttr,
	Usemap:                   trimAttr,
	Width:                    trimAttr, // uint
	Wrap:                     trimAttr,
	Xmlns:                    urlAttr,
}

var jsMimetypes = map[string]bool{
	"text/javascript":        true,
	"application/javascript": true,
}

// EntitiesMap are all named character entities.
var EntitiesMap = map[string][]byte{
	"AElig":                           []byte("&#198;"),
	"AMP":                             []byte("&"),
	"Aacute":                          []byte("&#193;"),
	"Abreve":                          []byte("&#258;"),
	"Acirc":                           []byte("&#194;"),
	"Agrave":                          []byte("&#192;"),
	"Alpha":                           []byte("&#913;"),
	"Amacr":                           []byte("&#256;"),
	"Aogon":                           []byte("&#260;"),
	"ApplyFunction":                   []byte("&af;"),
	"Aring":                           []byte("&#197;"),
	"Assign":                          []byte("&#8788;"),
	"Atilde":                          []byte("&#195;"),
	"Backslash":                       []byte("&#8726;"),
	"Barwed":                          []byte("&#8966;"),
	"Because":                         []byte("&#8757;"),
	"Bernoullis":                      []byte("&Bscr;"),
	"Breve":                           []byte("&#728;"),
	"Bumpeq":                          []byte("&bump;"),
	"Cacute":                          []byte("&#262;"),
	"CapitalDifferentialD":            []byte("&DD;"),
	"Cayleys":                         []byte("&Cfr;"),
	"Ccaron":                          []byte("&#268;"),
	"Ccedil":                          []byte("&#199;"),
	"Ccirc":                           []byte("&#264;"),
	"Cconint":                         []byte("&#8752;"),
	"Cedilla":                         []byte("&#184;"),
	"CenterDot":                       []byte("&#183;"),
	"CircleDot":                       []byte("&odot;"),
	"CircleMinus":                     []byte("&#8854;"),
	"CirclePlus":                      []byte("&#8853;"),
	"CircleTimes":                     []byte("&#8855;"),
	"ClockwiseContourIntegral":        []byte("&#8754;"),
	"CloseCurlyDoubleQuote":           []byte("&#8221;"),
	"CloseCurlyQuote":                 []byte("&#8217;"),
	"Congruent":                       []byte("&#8801;"),
	"Conint":                          []byte("&#8751;"),
	"ContourIntegral":                 []byte("&oint;"),
	"Coproduct":                       []byte("&#8720;"),
	"CounterClockwiseContourIntegral": []byte("&#8755;"),
	"CupCap":                          []byte("&#8781;"),
	"DDotrahd":                        []byte("&#10513;"),
	"Dagger":                          []byte("&#8225;"),
	"Dcaron":                          []byte("&#270;"),
	"Delta":                           []byte("&#916;"),
	"DiacriticalAcute":                []byte("&#180;"),
	"DiacriticalDot":                  []byte("&dot;"),
	"DiacriticalDoubleAcute":          []byte("&#733;"),
	"DiacriticalGrave":                []byte("`"),
	"DiacriticalTilde":                []byte("&#732;"),
	"Diamond":                         []byte("&diam;"),
	"DifferentialD":                   []byte("&dd;"),
	"DotDot":                          []byte("&#8412;"),
	"DotEqual":                        []byte("&#8784;"),
	"DoubleContourIntegral":           []byte("&#8751;"),
	"DoubleDot":                       []byte("&Dot;"),
	"DoubleDownArrow":                 []byte("&dArr;"),
	"DoubleLeftArrow":                 []byte("&lArr;"),
	"DoubleLeftRightArrow":            []byte("&iff;"),
	"DoubleLeftTee":                   []byte("&Dashv;"),
	"DoubleLongLeftArrow":             []byte("&xlArr;"),
	"DoubleLongLeftRightArrow":        []byte("&xhArr;"),
	"DoubleLongRightArrow":            []byte("&xrArr;"),
	"DoubleRightArrow":                []byte("&rArr;"),
	"DoubleRightTee":                  []byte("&#8872;"),
	"DoubleUpArrow":                   []byte("&uArr;"),
	"DoubleUpDownArrow":               []byte("&vArr;"),
	"DoubleVerticalBar":               []byte("&par;"),
	"DownArrow":                       []byte("&darr;"),
	"DownArrowBar":                    []byte("&#10515;"),
	"DownArrowUpArrow":                []byte("&#8693;"),
	"DownBreve":                       []byte("&#785;"),
	"DownLeftRightVector":             []byte("&#10576;"),
	"DownLeftTeeVector":               []byte("&#10590;"),
	"DownLeftVector":                  []byte("&#8637;"),
	"DownLeftVectorBar":               []byte("&#10582;"),
	"DownRightTeeVector":              []byte("&#10591;"),
	"DownRightVector":                 []byte("&#8641;"),
	"DownRightVectorBar":              []byte("&#10583;"),
	"DownTee":                         []byte("&top;"),
	"DownTeeArrow":                    []byte("&#8615;"),
	"Downarrow":                       []byte("&dArr;"),
	"Dstrok":                          []byte("&#272;"),
	"Eacute":                          []byte("&#201;"),
	"Ecaron":                          []byte("&#282;"),
	"Ecirc":                           []byte("&#202;"),
	"Egrave":                          []byte("&#200;"),
	"Element":                         []byte("&in;"),
	"Emacr":                           []byte("&#274;"),
	"EmptySmallSquare":                []byte("&#9723;"),
	"EmptyVerySmallSquare":            []byte("&#9643;"),
	"Eogon":                           []byte("&#280;"),
	"Epsilon":                         []byte("&#917;"),
	"EqualTilde":                      []byte("&esim;"),
	"Equilibrium":                     []byte("&#8652;"),
	"Exists":                          []byte("&#8707;"),
	"ExponentialE":                    []byte("&ee;"),
	"FilledSmallSquare":               []byte("&#9724;"),
	"FilledVerySmallSquare":           []byte("&squf;"),
	"ForAll":                          []byte("&#8704;"),
	"Fouriertrf":                      []byte("&Fscr;"),
	"GT":                              []byte(">"),
	"Gamma":                           []byte("&#915;"),
	"Gammad":                          []byte("&#988;"),
	"Gbreve":                          []byte("&#286;"),
	"Gcedil":                          []byte("&#290;"),
	"Gcirc":                           []byte("&#284;"),
	"GreaterEqual":                    []byte("&ge;"),
	"GreaterEqualLess":                []byte("&gel;"),
	"GreaterFullEqual":                []byte("&gE;"),
	"GreaterGreater":                  []byte("&#10914;"),
	"GreaterLess":                     []byte("&gl;"),
	"GreaterSlantEqual":               []byte("&ges;"),
	"GreaterTilde":                    []byte("&gsim;"),
	"HARDcy":                          []byte("&#1066;"),
	"Hacek":                           []byte("&#711;"),
	"Hat":                             []byte("^"),
	"Hcirc":                           []byte("&#292;"),
	"HilbertSpace":                    []byte("&Hscr;"),
	"HorizontalLine":                  []byte("&boxh;"),
	"Hstrok":                          []byte("&#294;"),
	"HumpDownHump":                    []byte("&bump;"),
	"HumpEqual":                       []byte("&#8783;"),
	"IJlig":                           []byte("&#306;"),
	"Iacute":                          []byte("&#205;"),
	"Icirc":                           []byte("&#206;"),
	"Ifr":                             []byte("&Im;"),
	"Igrave":                          []byte("&#204;"),
	"Imacr":                           []byte("&#298;"),
	"ImaginaryI":                      []byte("&ii;"),
	"Implies":                         []byte("&rArr;"),
	"Integral":                        []byte("&int;"),
	"Intersection":                    []byte("&xcap;"),
	"InvisibleComma":                  []byte("&ic;"),
	"InvisibleTimes":                  []byte("&it;"),
	"Iogon":                           []byte("&#302;"),
	"Itilde":                          []byte("&#296;"),
	"Jcirc":                           []byte("&#308;"),
	"Jsercy":                          []byte("&#1032;"),
	"Kappa":                           []byte("&#922;"),
	"Kcedil":                          []byte("&#310;"),
	"LT":                              []byte("<"),
	"Lacute":                          []byte("&#313;"),
	"Lambda":                          []byte("&#923;"),
	"Laplacetrf":                      []byte("&Lscr;"),
	"Lcaron":                          []byte("&#317;"),
	"Lcedil":                          []byte("&#315;"),
	"LeftAngleBracket":                []byte("&lang;"),
	"LeftArrow":                       []byte("&larr;"),
	"LeftArrowBar":                    []byte("&#8676;"),
	"LeftArrowRightArrow":             []byte("&#8646;"),
	"LeftCeiling":                     []byte("&#8968;"),
	"LeftDoubleBracket":               []byte("&lobrk;"),
	"LeftDownTeeVector":               []byte("&#10593;"),
	"LeftDownVector":                  []byte("&#8643;"),
	"LeftDownVectorBar":               []byte("&#10585;"),
	"LeftFloor":                       []byte("&#8970;"),
	"LeftRightArrow":                  []byte("&harr;"),
	"LeftRightVector":                 []byte("&#10574;"),
	"LeftTee":                         []byte("&#8867;"),
	"LeftTeeArrow":                    []byte("&#8612;"),
	"LeftTeeVector":                   []byte("&#10586;"),
	"LeftTriangle":                    []byte("&#8882;"),
	"LeftTriangleBar":                 []byte("&#10703;"),
	"LeftTriangleEqual":               []byte("&#8884;"),
	"LeftUpDownVector":                []byte("&#10577;"),
	"LeftUpTeeVector":                 []byte("&#10592;"),
	"LeftUpVector":                    []byte("&#8639;"),
	"LeftUpVectorBar":                 []byte("&#10584;"),
	"LeftVector":                      []byte("&#8636;"),
	"LeftVectorBar":                   []byte("&#10578;"),
	"Leftarrow":                       []byte("&lArr;"),
	"Leftrightarrow":                  []byte("&iff;"),
	"LessEqualGreater":                []byte("&leg;"),
	"LessFullEqual":                   []byte("&lE;"),
	"LessGreater":                     []byte("&lg;"),
	"LessLess":                        []byte("&#10913;"),
	"LessSlantEqual":                  []byte("&les;"),
	"LessTilde":                       []byte("&lsim;"),
	"Lleftarrow":                      []byte("&#8666;"),
	"Lmidot":                          []byte("&#319;"),
	"LongLeftArrow":                   []byte("&xlarr;"),
	"LongLeftRightArrow":              []byte("&xharr;"),
	"LongRightArrow":                  []byte("&xrarr;"),
	"Longleftarrow":                   []byte("&xlArr;"),
	"Longleftrightarrow":              []byte("&xhArr;"),
	"Longrightarrow":                  []byte("&xrArr;"),
	"LowerLeftArrow":                  []byte("&#8601;"),
	"LowerRightArrow":                 []byte("&#8600;"),
	"Lstrok":                          []byte("&#321;"),
	"MediumSpace":                     []byte("&#8287;"),
	"Mellintrf":                       []byte("&Mscr;"),
	"MinusPlus":                       []byte("&mp;"),
	"Nacute":                          []byte("&#323;"),
	"Ncaron":                          []byte("&#327;"),
	"Ncedil":                          []byte("&#325;"),
	"NegativeMediumSpace":             []byte("&#8203;"),
	"NegativeThickSpace":              []byte("&#8203;"),
	"NegativeThinSpace":               []byte("&#8203;"),
	"NegativeVeryThinSpace":           []byte("&#8203;"),
	"NestedGreaterGreater":            []byte("&Gt;"),
	"NestedLessLess":                  []byte("&Lt;"),
	"NewLine":                         []byte("\n"),
	"NoBreak":                         []byte("&#8288;"),
	"NonBreakingSpace":                []byte("&#160;"),
	"NotCongruent":                    []byte("&#8802;"),
	"NotCupCap":                       []byte("&#8813;"),
	"NotDoubleVerticalBar":            []byte("&npar;"),
	"NotElement":                      []byte("&#8713;"),
	"NotEqual":                        []byte("&ne;"),
	"NotExists":                       []byte("&#8708;"),
	"NotGreater":                      []byte("&ngt;"),
	"NotGreaterEqual":                 []byte("&nge;"),
	"NotGreaterLess":                  []byte("&ntgl;"),
	"NotGreaterTilde":                 []byte("&#8821;"),
	"NotLeftTriangle":                 []byte("&#8938;"),
	"NotLeftTriangleEqual":            []byte("&#8940;"),
	"NotLess":                         []byte("&nlt;"),
	"NotLessEqual":                    []byte("&nle;"),
	"NotLessGreater":                  []byte("&ntlg;"),
	"NotLessTilde":                    []byte("&#8820;"),
	"NotPrecedes":                     []byte("&npr;"),
	"NotPrecedesSlantEqual":           []byte("&#8928;"),
	"NotReverseElement":               []byte("&#8716;"),
	"NotRightTriangle":                []byte("&#8939;"),
	"NotRightTriangleEqual":           []byte("&#8941;"),
	"NotSquareSubsetEqual":            []byte("&#8930;"),
	"NotSquareSupersetEqual":          []byte("&#8931;"),
	"NotSubsetEqual":                  []byte("&#8840;"),
	"NotSucceeds":                     []byte("&nsc;"),
	"NotSucceedsSlantEqual":           []byte("&#8929;"),
	"NotSupersetEqual":                []byte("&#8841;"),
	"NotTilde":                        []byte("&nsim;"),
	"NotTildeEqual":                   []byte("&#8772;"),
	"NotTildeFullEqual":               []byte("&#8775;"),
	"NotTildeTilde":                   []byte("&nap;"),
	"NotVerticalBar":                  []byte("&nmid;"),
	"Ntilde":                          []byte("&#209;"),
	"OElig":                           []byte("&#338;"),
	"Oacute":                          []byte("&#211;"),
	"Ocirc":                           []byte("&#212;"),
	"Odblac":                          []byte("&#336;"),
	"Ograve":                          []byte("&#210;"),
	"Omacr":                           []byte("&#332;"),
	"Omega":                           []byte("&ohm;"),
	"Omicron":                         []byte("&#927;"),
	"OpenCurlyDoubleQuote":            []byte("&#8220;"),
	"OpenCurlyQuote":                  []byte("&#8216;"),
	"Oslash":                          []byte("&#216;"),
	"Otilde":                          []byte("&#213;"),
	"OverBar":                         []byte("&#8254;"),
	"OverBrace":                       []byte("&#9182;"),
	"OverBracket":                     []byte("&tbrk;"),
	"OverParenthesis":                 []byte("&#9180;"),
	"PartialD":                        []byte("&part;"),
	"PlusMinus":                       []byte("&pm;"),
	"Poincareplane":                   []byte("&Hfr;"),
	"Precedes":                        []byte("&pr;"),
	"PrecedesEqual":                   []byte("&pre;"),
	"PrecedesSlantEqual":              []byte("&#8828;"),
	"PrecedesTilde":                   []byte("&#8830;"),
	"Product":                         []byte("&prod;"),
	"Proportion":                      []byte("&#8759;"),
	"Proportional":                    []byte("&prop;"),
	"QUOT":                            []byte("\""),
	"Racute":                          []byte("&#340;"),
	"Rcaron":                          []byte("&#344;"),
	"Rcedil":                          []byte("&#342;"),
	"ReverseElement":                  []byte("&ni;"),
	"ReverseEquilibrium":              []byte("&#8651;"),
	"ReverseUpEquilibrium":            []byte("&duhar;"),
	"Rfr":                             []byte("&Re;"),
	"RightAngleBracket":               []byte("&rang;"),
	"RightArrow":                      []byte("&rarr;"),
	"RightArrowBar":                   []byte("&#8677;"),
	"RightArrowLeftArrow":             []byte("&#8644;"),
	"RightCeiling":                    []byte("&#8969;"),
	"RightDoubleBracket":              []byte("&robrk;"),
	"RightDownTeeVector":              []byte("&#10589;"),
	"RightDownVector":                 []byte("&#8642;"),
	"RightDownVectorBar":              []byte("&#10581;"),
	"RightFloor":                      []byte("&#8971;"),
	"RightTee":                        []byte("&#8866;"),
	"RightTeeArrow":                   []byte("&map;"),
	"RightTeeVector":                  []byte("&#10587;"),
	"RightTriangle":                   []byte("&#8883;"),
	"RightTriangleBar":                []byte("&#10704;"),
	"RightTriangleEqual":              []byte("&#8885;"),
	"RightUpDownVector":               []byte("&#10575;"),
	"RightUpTeeVector":                []byte("&#10588;"),
	"RightUpVector":                   []byte("&#8638;"),
	"RightUpVectorBar":                []byte("&#10580;"),
	"RightVector":                     []byte("&#8640;"),
	"RightVectorBar":                  []byte("&#10579;"),
	"Rightarrow":                      []byte("&rArr;"),
	"RoundImplies":                    []byte("&#10608;"),
	"Rrightarrow":                     []byte("&#8667;"),
	"RuleDelayed":                     []byte("&#10740;"),
	"SHCHcy":                          []byte("&#1065;"),
	"SOFTcy":                          []byte("&#1068;"),
	"Sacute":                          []byte("&#346;"),
	"Scaron":                          []byte("&#352;"),
	"Scedil":                          []byte("&#350;"),
	"Scirc":                           []byte("&#348;"),
	"ShortDownArrow":                  []byte("&darr;"),
	"ShortLeftArrow":                  []byte("&larr;"),
	"ShortRightArrow":                 []byte("&rarr;"),
	"ShortUpArrow":                    []byte("&uarr;"),
	"Sigma":                           []byte("&#931;"),
	"SmallCircle":                     []byte("&#8728;"),
	"Square":                          []byte("&squ;"),
	"SquareIntersection":              []byte("&#8851;"),
	"SquareSubset":                    []byte("&#8847;"),
	"SquareSubsetEqual":               []byte("&#8849;"),
	"SquareSuperset":                  []byte("&#8848;"),
	"SquareSupersetEqual":             []byte("&#8850;"),
	"SquareUnion":                     []byte("&#8852;"),
	"Subset":                          []byte("&Sub;"),
	"SubsetEqual":                     []byte("&sube;"),
	"Succeeds":                        []byte("&sc;"),
	"SucceedsEqual":                   []byte("&sce;"),
	"SucceedsSlantEqual":              []byte("&#8829;"),
	"SucceedsTilde":                   []byte("&#8831;"),
	"SuchThat":                        []byte("&ni;"),
	"Superset":                        []byte("&sup;"),
	"SupersetEqual":                   []byte("&supe;"),
	"Supset":                          []byte("&Sup;"),
	"THORN":                           []byte("&#222;"),
	"Tab":                             []byte("\t"),
	"Tcaron":                          []byte("&#356;"),
	"Tcedil":                          []byte("&#354;"),
	"Therefore":                       []byte("&#8756;"),
	"Theta":                           []byte("&#920;"),
	"ThinSpace":                       []byte("&#8201;"),
	"Tilde":                           []byte("&sim;"),
	"TildeEqual":                      []byte("&sime;"),
	"TildeFullEqual":                  []byte("&cong;"),
	"TildeTilde":                      []byte("&ap;"),
	"TripleDot":                       []byte("&tdot;"),
	"Tstrok":                          []byte("&#358;"),
	"Uacute":                          []byte("&#218;"),
	"Uarrocir":                        []byte("&#10569;"),
	"Ubreve":                          []byte("&#364;"),
	"Ucirc":                           []byte("&#219;"),
	"Udblac":                          []byte("&#368;"),
	"Ugrave":                          []byte("&#217;"),
	"Umacr":                           []byte("&#362;"),
	"UnderBar":                        []byte("_"),
	"UnderBrace":                      []byte("&#9183;"),
	"UnderBracket":                    []byte("&bbrk;"),
	"UnderParenthesis":                []byte("&#9181;"),
	"Union":                           []byte("&xcup;"),
	"UnionPlus":                       []byte("&#8846;"),
	"Uogon":                           []byte("&#370;"),
	"UpArrow":                         []byte("&uarr;"),
	"UpArrowBar":                      []byte("&#10514;"),
	"UpArrowDownArrow":                []byte("&#8645;"),
	"UpDownArrow":                     []byte("&varr;"),
	"UpEquilibrium":                   []byte("&udhar;"),
	"UpTee":                           []byte("&bot;"),
	"UpTeeArrow":                      []byte("&#8613;"),
	"Uparrow":                         []byte("&uArr;"),
	"Updownarrow":                     []byte("&vArr;"),
	"UpperLeftArrow":                  []byte("&#8598;"),
	"UpperRightArrow":                 []byte("&#8599;"),
	"Upsilon":                         []byte("&#933;"),
	"Uring":                           []byte("&#366;"),
	"Utilde":                          []byte("&#360;"),
	"Verbar":                          []byte("&Vert;"),
	"VerticalBar":                     []byte("&mid;"),
	"VerticalLine":                    []byte("|"),
	"VerticalSeparator":               []byte("&#10072;"),
	"VerticalTilde":                   []byte("&wr;"),
	"VeryThinSpace":                   []byte("&#8202;"),
	"Vvdash":                          []byte("&#8874;"),
	"Wcirc":                           []byte("&#372;"),
	"Yacute":                          []byte("&#221;"),
	"Ycirc":                           []byte("&#374;"),
	"Zacute":                          []byte("&#377;"),
	"Zcaron":                          []byte("&#381;"),
	"ZeroWidthSpace":                  []byte("&#8203;"),
	"aacute":                          []byte("&#225;"),
	"abreve":                          []byte("&#259;"),
	"acirc":                           []byte("&#226;"),
	"acute":                           []byte("&#180;"),
	"aelig":                           []byte("&#230;"),
	"agrave":                          []byte("&#224;"),
	"alefsym":                         []byte("&#8501;"),
	"alpha":                           []byte("&#945;"),
	"amacr":                           []byte("&#257;"),
	"amp":                             []byte("&"),
	"andslope":                        []byte("&#10840;"),
	"angle":                           []byte("&ang;"),
	"angmsd":                          []byte("&#8737;"),
	"angmsdaa":                        []byte("&#10664;"),
	"angmsdab":                        []byte("&#10665;"),
	"angmsdac":                        []byte("&#10666;"),
	"angmsdad":                        []byte("&#10667;"),
	"angmsdae":                        []byte("&#10668;"),
	"angmsdaf":                        []byte("&#10669;"),
	"angmsdag":                        []byte("&#10670;"),
	"angmsdah":                        []byte("&#10671;"),
	"angrtvb":                         []byte("&#8894;"),
	"angrtvbd":                        []byte("&#10653;"),
	"angsph":                          []byte("&#8738;"),
	"angst":                           []byte("&#197;"),
	"angzarr":                         []byte("&#9084;"),
	"aogon":                           []byte("&#261;"),
	"apos":                            []byte("'"),
	"approx":                          []byte("&ap;"),
	"approxeq":                        []byte("&ape;"),
	"aring":                           []byte("&#229;"),
	"ast":                             []byte("*"),
	"asymp":                           []byte("&ap;"),
	"asympeq":                         []byte("&#8781;"),
	"atilde":                          []byte("&#227;"),
	"awconint":                        []byte("&#8755;"),
	"backcong":                        []byte("&#8780;"),
	"backepsilon":                     []byte("&#1014;"),
	"backprime":                       []byte("&#8245;"),
	"backsim":                         []byte("&bsim;"),
	"backsimeq":                       []byte("&#8909;"),
	"barvee":                          []byte("&#8893;"),
	"barwed":                          []byte("&#8965;"),
	"barwedge":                        []byte("&#8965;"),
	"bbrktbrk":                        []byte("&#9142;"),
	"becaus":                          []byte("&#8757;"),
	"because":                         []byte("&#8757;"),
	"bemptyv":                         []byte("&#10672;"),
	"bernou":                          []byte("&Bscr;"),
	"between":                         []byte("&#8812;"),
	"bigcap":                          []byte("&xcap;"),
	"bigcirc":                         []byte("&#9711;"),
	"bigcup":                          []byte("&xcup;"),
	"bigodot":                         []byte("&xodot;"),
	"bigoplus":                        []byte("&#10753;"),
	"bigotimes":                       []byte("&#10754;"),
	"bigsqcup":                        []byte("&#10758;"),
	"bigstar":                         []byte("&#9733;"),
	"bigtriangledown":                 []byte("&#9661;"),
	"bigtriangleup":                   []byte("&#9651;"),
	"biguplus":                        []byte("&#10756;"),
	"bigvee":                          []byte("&Vee;"),
	"bigwedge":                        []byte("&#8896;"),
	"bkarow":                          []byte("&rbarr;"),
	"blacklozenge":                    []byte("&lozf;"),
	"blacksquare":                     []byte("&squf;"),
	"blacktriangle":                   []byte("&#9652;"),
	"blacktriangledown":               []byte("&#9662;"),
	"blacktriangleleft":               []byte("&#9666;"),
	"blacktriangleright":              []byte("&#9656;"),
	"bottom":                          []byte("&bot;"),
	"bowtie":                          []byte("&#8904;"),
	"boxminus":                        []byte("&#8863;"),
	"boxplus":                         []byte("&#8862;"),
	"boxtimes":                        []byte("&#8864;"),
	"bprime":                          []byte("&#8245;"),
	"breve":                           []byte("&#728;"),
	"brvbar":                          []byte("&#166;"),
	"bsol":                            []byte("\\"),
	"bsolhsub":                        []byte("&#10184;"),
	"bullet":                          []byte("&bull;"),
	"bumpeq":                          []byte("&#8783;"),
	"cacute":                          []byte("&#263;"),
	"capbrcup":                        []byte("&#10825;"),
	"caron":                           []byte("&#711;"),
	"ccaron":                          []byte("&#269;"),
	"ccedil":                          []byte("&#231;"),
	"ccirc":                           []byte("&#265;"),
	"ccupssm":                         []byte("&#10832;"),
	"cedil":                           []byte("&#184;"),
	"cemptyv":                         []byte("&#10674;"),
	"centerdot":                       []byte("&#183;"),
	"checkmark":                       []byte("&check;"),
	"circeq":                          []byte("&cire;"),
	"circlearrowleft":                 []byte("&#8634;"),
	"circlearrowright":                []byte("&#8635;"),
	"circledR":                        []byte("&REG;"),
	"circledS":                        []byte("&oS;"),
	"circledast":                      []byte("&oast;"),
	"circledcirc":                     []byte("&ocir;"),
	"circleddash":                     []byte("&#8861;"),
	"cirfnint":                        []byte("&#10768;"),
	"cirscir":                         []byte("&#10690;"),
	"clubsuit":                        []byte("&#9827;"),
	"colon":                           []byte(":"),
	"colone":                          []byte("&#8788;"),
	"coloneq":                         []byte("&#8788;"),
	"comma":                           []byte(","),
	"commat":                          []byte("@"),
	"compfn":                          []byte("&#8728;"),
	"complement":                      []byte("&comp;"),
	"complexes":                       []byte("&Copf;"),
	"congdot":                         []byte("&#10861;"),
	"conint":                          []byte("&oint;"),
	"coprod":                          []byte("&#8720;"),
	"copysr":                          []byte("&#8471;"),
	"cudarrl":                         []byte("&#10552;"),
	"cudarrr":                         []byte("&#10549;"),
	"cularr":                          []byte("&#8630;"),
	"cularrp":                         []byte("&#10557;"),
	"cupbrcap":                        []byte("&#10824;"),
	"cupdot":                          []byte("&#8845;"),
	"curarr":                          []byte("&#8631;"),
	"curarrm":                         []byte("&#10556;"),
	"curlyeqprec":                     []byte("&#8926;"),
	"curlyeqsucc":                     []byte("&#8927;"),
	"curlyvee":                        []byte("&#8910;"),
	"curlywedge":                      []byte("&#8911;"),
	"curren":                          []byte("&#164;"),
	"curvearrowleft":                  []byte("&#8630;"),
	"curvearrowright":                 []byte("&#8631;"),
	"cwconint":                        []byte("&#8754;"),
	"cylcty":                          []byte("&#9005;"),
	"dagger":                          []byte("&#8224;"),
	"daleth":                          []byte("&#8504;"),
	"dbkarow":                         []byte("&rBarr;"),
	"dblac":                           []byte("&#733;"),
	"dcaron":                          []byte("&#271;"),
	"ddagger":                         []byte("&#8225;"),
	"ddotseq":                         []byte("&eDDot;"),
	"delta":                           []byte("&#948;"),
	"demptyv":                         []byte("&#10673;"),
	"diamond":                         []byte("&diam;"),
	"diamondsuit":                     []byte("&#9830;"),
	"digamma":                         []byte("&#989;"),
	"divide":                          []byte("&div;"),
	"divideontimes":                   []byte("&#8903;"),
	"divonx":                          []byte("&#8903;"),
	"dlcorn":                          []byte("&#8990;"),
	"dlcrop":                          []byte("&#8973;"),
	"dollar":                          []byte("$"),
	"doteqdot":                        []byte("&eDot;"),
	"dotminus":                        []byte("&#8760;"),
	"dotplus":                         []byte("&#8724;"),
	"dotsquare":                       []byte("&#8865;"),
	"doublebarwedge":                  []byte("&#8966;"),
	"downarrow":                       []byte("&darr;"),
	"downdownarrows":                  []byte("&#8650;"),
	"downharpoonleft":                 []byte("&#8643;"),
	"downharpoonright":                []byte("&#8642;"),
	"drbkarow":                        []byte("&RBarr;"),
	"drcorn":                          []byte("&#8991;"),
	"drcrop":                          []byte("&#8972;"),
	"dstrok":                          []byte("&#273;"),
	"dwangle":                         []byte("&#10662;"),
	"dzigrarr":                        []byte("&#10239;"),
	"eacute":                          []byte("&#233;"),
	"ecaron":                          []byte("&#283;"),
	"ecirc":                           []byte("&#234;"),
	"ecolon":                          []byte("&#8789;"),
	"egrave":                          []byte("&#232;"),
	"elinters":                        []byte("&#9191;"),
	"emacr":                           []byte("&#275;"),
	"emptyset":                        []byte("&#8709;"),
	"emptyv":                          []byte("&#8709;"),
	"emsp13":                          []byte("&#8196;"),
	"emsp14":                          []byte("&#8197;"),
	"eogon":                           []byte("&#281;"),
	"epsilon":                         []byte("&#949;"),
	"eqcirc":                          []byte("&ecir;"),
	"eqcolon":                         []byte("&#8789;"),
	"eqsim":                           []byte("&esim;"),
	"eqslantgtr":                      []byte("&egs;"),
	"eqslantless":                     []byte("&els;"),
	"equals":                          []byte("="),
	"equest":                          []byte("&#8799;"),
	"equivDD":                         []byte("&#10872;"),
	"eqvparsl":                        []byte("&#10725;"),
	"excl":                            []byte("!"),
	"expectation":                     []byte("&Escr;"),
	"exponentiale":                    []byte("&ee;"),
	"fallingdotseq":                   []byte("&#8786;"),
	"female":                          []byte("&#9792;"),
	"forall":                          []byte("&#8704;"),
	"fpartint":                        []byte("&#10765;"),
	"frac12":                          []byte("&#189;"),
	"frac13":                          []byte("&#8531;"),
	"frac14":                          []byte("&#188;"),
	"frac15":                          []byte("&#8533;"),
	"frac16":                          []byte("&#8537;"),
	"frac18":                          []byte("&#8539;"),
	"frac23":                          []byte("&#8532;"),
	"frac25":                          []byte("&#8534;"),
	"frac34":                          []byte("&#190;"),
	"frac35":                          []byte("&#8535;"),
	"frac38":                          []byte("&#8540;"),
	"frac45":                          []byte("&#8536;"),
	"frac56":                          []byte("&#8538;"),
	"frac58":                          []byte("&#8541;"),
	"frac78":                          []byte("&#8542;"),
	"gacute":                          []byte("&#501;"),
	"gamma":                           []byte("&#947;"),
	"gammad":                          []byte("&#989;"),
	"gbreve":                          []byte("&#287;"),
	"gcirc":                           []byte("&#285;"),
	"geq":                             []byte("&ge;"),
	"geqq":                            []byte("&gE;"),
	"geqslant":                        []byte("&ges;"),
	"gesdoto":                         []byte("&#10882;"),
	"gesdotol":                        []byte("&#10884;"),
	"ggg":                             []byte("&Gg;"),
	"gnapprox":                        []byte("&gnap;"),
	"gneq":                            []byte("&gne;"),
	"gneqq":                           []byte("&gnE;"),
	"grave":                           []byte("`"),
	"gt":                              []byte(">"),
	"gtquest":                         []byte("&#10876;"),
	"gtrapprox":                       []byte("&gap;"),
	"gtrdot":                          []byte("&#8919;"),
	"gtreqless":                       []byte("&gel;"),
	"gtreqqless":                      []byte("&gEl;"),
	"gtrless":                         []byte("&gl;"),
	"gtrsim":                          []byte("&gsim;"),
	"hArr":                            []byte("&iff;"),
	"hairsp":                          []byte("&#8202;"),
	"hamilt":                          []byte("&Hscr;"),
	"hardcy":                          []byte("&#1098;"),
	"harrcir":                         []byte("&#10568;"),
	"hcirc":                           []byte("&#293;"),
	"hearts":                          []byte("&#9829;"),
	"heartsuit":                       []byte("&#9829;"),
	"hellip":                          []byte("&mldr;"),
	"hercon":                          []byte("&#8889;"),
	"hksearow":                        []byte("&#10533;"),
	"hkswarow":                        []byte("&#10534;"),
	"homtht":                          []byte("&#8763;"),
	"hookleftarrow":                   []byte("&#8617;"),
	"hookrightarrow":                  []byte("&#8618;"),
	"horbar":                          []byte("&#8213;"),
	"hslash":                          []byte("&hbar;"),
	"hstrok":                          []byte("&#295;"),
	"hybull":                          []byte("&#8259;"),
	"hyphen":                          []byte("&dash;"),
	"iacute":                          []byte("&#237;"),
	"icirc":                           []byte("&#238;"),
	"iexcl":                           []byte("&#161;"),
	"igrave":                          []byte("&#236;"),
	"iiiint":                          []byte("&qint;"),
	"iiint":                           []byte("&tint;"),
	"ijlig":                           []byte("&#307;"),
	"imacr":                           []byte("&#299;"),
	"image":                           []byte("&Im;"),
	"imagline":                        []byte("&Iscr;"),
	"imagpart":                        []byte("&Im;"),
	"imath":                           []byte("&#305;"),
	"imped":                           []byte("&#437;"),
	"incare":                          []byte("&#8453;"),
	"infintie":                        []byte("&#10717;"),
	"inodot":                          []byte("&#305;"),
	"intcal":                          []byte("&#8890;"),
	"integers":                        []byte("&Zopf;"),
	"intercal":                        []byte("&#8890;"),
	"intlarhk":                        []byte("&#10775;"),
	"intprod":                         []byte("&iprod;"),
	"iogon":                           []byte("&#303;"),
	"iquest":                          []byte("&#191;"),
	"isin":                            []byte("&in;"),
	"isindot":                         []byte("&#8949;"),
	"isinsv":                          []byte("&#8947;"),
	"isinv":                           []byte("&in;"),
	"itilde":                          []byte("&#297;"),
	"jcirc":                           []byte("&#309;"),
	"jmath":                           []byte("&#567;"),
	"jsercy":                          []byte("&#1112;"),
	"kappa":                           []byte("&#954;"),
	"kappav":                          []byte("&#1008;"),
	"kcedil":                          []byte("&#311;"),
	"kgreen":                          []byte("&#312;"),
	"lacute":                          []byte("&#314;"),
	"laemptyv":                        []byte("&#10676;"),
	"lagran":                          []byte("&Lscr;"),
	"lambda":                          []byte("&#955;"),
	"langle":                          []byte("&lang;"),
	"laquo":                           []byte("&#171;"),
	"larrbfs":                         []byte("&#10527;"),
	"larrhk":                          []byte("&#8617;"),
	"larrlp":                          []byte("&#8619;"),
	"larrsim":                         []byte("&#10611;"),
	"larrtl":                          []byte("&#8610;"),
	"lbrace":                          []byte("{"),
	"lbrack":                          []byte("["),
	"lbrksld":                         []byte("&#10639;"),
	"lbrkslu":                         []byte("&#10637;"),
	"lcaron":                          []byte("&#318;"),
	"lcedil":                          []byte("&#316;"),
	"lcub":                            []byte("{"),
	"ldquor":                          []byte("&#8222;"),
	"ldrdhar":                         []byte("&#10599;"),
	"ldrushar":                        []byte("&#10571;"),
	"leftarrow":                       []byte("&larr;"),
	"leftarrowtail":                   []byte("&#8610;"),
	"leftharpoondown":                 []byte("&#8637;"),
	"leftharpoonup":                   []byte("&#8636;"),
	"leftleftarrows":                  []byte("&#8647;"),
	"leftrightarrow":                  []byte("&harr;"),
	"leftrightarrows":                 []byte("&#8646;"),
	"leftrightharpoons":               []byte("&#8651;"),
	"leftrightsquigarrow":             []byte("&#8621;"),
	"leftthreetimes":                  []byte("&#8907;"),
	"leq":                             []byte("&le;"),
	"leqq":                            []byte("&lE;"),
	"leqslant":                        []byte("&les;"),
	"lesdoto":                         []byte("&#10881;"),
	"lesdotor":                        []byte("&#10883;"),
	"lessapprox":                      []byte("&lap;"),
	"lessdot":                         []byte("&#8918;"),
	"lesseqgtr":                       []byte("&leg;"),
	"lesseqqgtr":                      []byte("&lEg;"),
	"lessgtr":                         []byte("&lg;"),
	"lesssim":                         []byte("&lsim;"),
	"lfloor":                          []byte("&#8970;"),
	"llcorner":                        []byte("&#8990;"),
	"lmidot":                          []byte("&#320;"),
	"lmoust":                          []byte("&#9136;"),
	"lmoustache":                      []byte("&#9136;"),
	"lnapprox":                        []byte("&lnap;"),
	"lneq":                            []byte("&lne;"),
	"lneqq":                           []byte("&lnE;"),
	"longleftarrow":                   []byte("&xlarr;"),
	"longleftrightarrow":              []byte("&xharr;"),
	"longmapsto":                      []byte("&xmap;"),
	"longrightarrow":                  []byte("&xrarr;"),
	"looparrowleft":                   []byte("&#8619;"),
	"looparrowright":                  []byte("&#8620;"),
	"lotimes":                         []byte("&#10804;"),
	"lowast":                          []byte("&#8727;"),
	"lowbar":                          []byte("_"),
	"lozenge":                         []byte("&loz;"),
	"lpar":                            []byte("("),
	"lrcorner":                        []byte("&#8991;"),
	"lsaquo":                          []byte("&#8249;"),
	"lsqb":                            []byte("["),
	"lsquor":                          []byte("&#8218;"),
	"lstrok":                          []byte("&#322;"),
	"lt":                              []byte("<"),
	"lthree":                          []byte("&#8907;"),
	"ltimes":                          []byte("&#8905;"),
	"ltquest":                         []byte("&#10875;"),
	"lurdshar":                        []byte("&#10570;"),
	"luruhar":                         []byte("&#10598;"),
	"maltese":                         []byte("&malt;"),
	"mapsto":                          []byte("&map;"),
	"mapstodown":                      []byte("&#8615;"),
	"mapstoleft":                      []byte("&#8612;"),
	"mapstoup":                        []byte("&#8613;"),
	"marker":                          []byte("&#9646;"),
	"measuredangle":                   []byte("&#8737;"),
	"micro":                           []byte("&#181;"),
	"midast":                          []byte("*"),
	"middot":                          []byte("&#183;"),
	"minusb":                          []byte("&#8863;"),
	"minusd":                          []byte("&#8760;"),
	"minusdu":                         []byte("&#10794;"),
	"mnplus":                          []byte("&mp;"),
	"models":                          []byte("&#8871;"),
	"mstpos":                          []byte("&ac;"),
	"multimap":                        []byte("&#8888;"),
	"nLeftarrow":                      []byte("&#8653;"),
	"nLeftrightarrow":                 []byte("&#8654;"),
	"nRightarrow":                     []byte("&#8655;"),
	"nVDash":                          []byte("&#8879;"),
	"nVdash":                          []byte("&#8878;"),
	"nabla":                           []byte("&Del;"),
	"nacute":                          []byte("&#324;"),
	"napos":                           []byte("&#329;"),
	"napprox":                         []byte("&nap;"),
	"natural":                         []byte("&#9838;"),
	"naturals":                        []byte("&Nopf;"),
	"ncaron":                          []byte("&#328;"),
	"ncedil":                          []byte("&#326;"),
	"nearrow":                         []byte("&#8599;"),
	"nequiv":                          []byte("&#8802;"),
	"nesear":                          []byte("&toea;"),
	"nexist":                          []byte("&#8708;"),
	"nexists":                         []byte("&#8708;"),
	"ngeq":                            []byte("&nge;"),
	"ngtr":                            []byte("&ngt;"),
	"niv":                             []byte("&ni;"),
	"nleftarrow":                      []byte("&#8602;"),
	"nleftrightarrow":                 []byte("&#8622;"),
	"nleq":                            []byte("&nle;"),
	"nless":                           []byte("&nlt;"),
	"nltrie":                          []byte("&#8940;"),
	"notinva":                         []byte("&#8713;"),
	"notinvb":                         []byte("&#8951;"),
	"notinvc":                         []byte("&#8950;"),
	"notniva":                         []byte("&#8716;"),
	"notnivb":                         []byte("&#8958;"),
	"notnivc":                         []byte("&#8957;"),
	"nparallel":                       []byte("&npar;"),
	"npolint":                         []byte("&#10772;"),
	"nprcue":                          []byte("&#8928;"),
	"nprec":                           []byte("&npr;"),
	"nrightarrow":                     []byte("&#8603;"),
	"nrtrie":                          []byte("&#8941;"),
	"nsccue":                          []byte("&#8929;"),
	"nshortmid":                       []byte("&nmid;"),
	"nshortparallel":                  []byte("&npar;"),
	"nsimeq":                          []byte("&#8772;"),
	"nsmid":                           []byte("&nmid;"),
	"nspar":                           []byte("&npar;"),
	"nsqsube":                         []byte("&#8930;"),
	"nsqsupe":                         []byte("&#8931;"),
	"nsubseteq":                       []byte("&#8840;"),
	"nsucc":                           []byte("&nsc;"),
	"nsupseteq":                       []byte("&#8841;"),
	"ntilde":                          []byte("&#241;"),
	"ntriangleleft":                   []byte("&#8938;"),
	"ntrianglelefteq":                 []byte("&#8940;"),
	"ntriangleright":                  []byte("&#8939;"),
	"ntrianglerighteq":                []byte("&#8941;"),
	"num":                             []byte("#"),
	"numero":                          []byte("&#8470;"),
	"nvDash":                          []byte("&#8877;"),
	"nvdash":                          []byte("&#8876;"),
	"nvinfin":                         []byte("&#10718;"),
	"nwarrow":                         []byte("&#8598;"),
	"oacute":                          []byte("&#243;"),
	"ocirc":                           []byte("&#244;"),
	"odblac":                          []byte("&#337;"),
	"oelig":                           []byte("&#339;"),
	"ograve":                          []byte("&#242;"),
	"olcross":                         []byte("&#10683;"),
	"omacr":                           []byte("&#333;"),
	"omega":                           []byte("&#969;"),
	"omicron":                         []byte("&#959;"),
	"ominus":                          []byte("&#8854;"),
	"order":                           []byte("&oscr;"),
	"orderof":                         []byte("&oscr;"),
	"origof":                          []byte("&#8886;"),
	"orslope":                         []byte("&#10839;"),
	"oslash":                          []byte("&#248;"),
	"otilde":                          []byte("&#245;"),
	"otimes":                          []byte("&#8855;"),
	"otimesas":                        []byte("&#10806;"),
	"parallel":                        []byte("&par;"),
	"percnt":                          []byte("%"),
	"period":                          []byte("."),
	"permil":                          []byte("&#8240;"),
	"perp":                            []byte("&bot;"),
	"pertenk":                         []byte("&#8241;"),
	"phmmat":                          []byte("&Mscr;"),
	"pitchfork":                       []byte("&fork;"),
	"planck":                          []byte("&hbar;"),
	"planckh":                         []byte("&#8462;"),
	"plankv":                          []byte("&hbar;"),
	"plus":                            []byte("+"),
	"plusacir":                        []byte("&#10787;"),
	"pluscir":                         []byte("&#10786;"),
	"plusdo":                          []byte("&#8724;"),
	"plusmn":                          []byte("&pm;"),
	"plussim":                         []byte("&#10790;"),
	"plustwo":                         []byte("&#10791;"),
	"pointint":                        []byte("&#10773;"),
	"pound":                           []byte("&#163;"),
	"prec":                            []byte("&pr;"),
	"precapprox":                      []byte("&prap;"),
	"preccurlyeq":                     []byte("&#8828;"),
	"preceq":                          []byte("&pre;"),
	"precnapprox":                     []byte("&prnap;"),
	"precneqq":                        []byte("&prnE;"),
	"precnsim":                        []byte("&#8936;"),
	"precsim":                         []byte("&#8830;"),
	"primes":                          []byte("&Popf;"),
	"prnsim":                          []byte("&#8936;"),
	"profalar":                        []byte("&#9006;"),
	"profline":                        []byte("&#8978;"),
	"profsurf":                        []byte("&#8979;"),
	"propto":                          []byte("&prop;"),
	"prurel":                          []byte("&#8880;"),
	"puncsp":                          []byte("&#8200;"),
	"qprime":                          []byte("&#8279;"),
	"quaternions":                     []byte("&Hopf;"),
	"quatint":                         []byte("&#10774;"),
	"quest":                           []byte("?"),
	"questeq":                         []byte("&#8799;"),
	"quot":                            []byte("\""),
	"racute":                          []byte("&#341;"),
	"radic":                           []byte("&Sqrt;"),
	"raemptyv":                        []byte("&#10675;"),
	"rangle":                          []byte("&rang;"),
	"raquo":                           []byte("&#187;"),
	"rarrbfs":                         []byte("&#10528;"),
	"rarrhk":                          []byte("&#8618;"),
	"rarrlp":                          []byte("&#8620;"),
	"rarrsim":                         []byte("&#10612;"),
	"rarrtl":                          []byte("&#8611;"),
	"rationals":                       []byte("&Qopf;"),
	"rbrace":                          []byte("}"),
	"rbrack":                          []byte("]"),
	"rbrksld":                         []byte("&#10638;"),
	"rbrkslu":                         []byte("&#10640;"),
	"rcaron":                          []byte("&#345;"),
	"rcedil":                          []byte("&#343;"),
	"rcub":                            []byte("}"),
	"rdldhar":                         []byte("&#10601;"),
	"rdquor":                          []byte("&#8221;"),
	"real":                            []byte("&Re;"),
	"realine":                         []byte("&Rscr;"),
	"realpart":                        []byte("&Re;"),
	"reals":                           []byte("&Ropf;"),
	"rfloor":                          []byte("&#8971;"),
	"rightarrow":                      []byte("&rarr;"),
	"rightarrowtail":                  []byte("&#8611;"),
	"rightharpoondown":                []byte("&#8641;"),
	"rightharpoonup":                  []byte("&#8640;"),
	"rightleftarrows":                 []byte("&#8644;"),
	"rightleftharpoons":               []byte("&#8652;"),
	"rightrightarrows":                []byte("&#8649;"),
	"rightsquigarrow":                 []byte("&#8605;"),
	"rightthreetimes":                 []byte("&#8908;"),
	"risingdotseq":                    []byte("&#8787;"),
	"rmoust":                          []byte("&#9137;"),
	"rmoustache":                      []byte("&#9137;"),
	"rotimes":                         []byte("&#10805;"),
	"rpar":                            []byte(")"),
	"rppolint":                        []byte("&#10770;"),
	"rsaquo":                          []byte("&#8250;"),
	"rsqb":                            []byte("]"),
	"rsquor":                          []byte("&#8217;"),
	"rthree":                          []byte("&#8908;"),
	"rtimes":                          []byte("&#8906;"),
	"rtriltri":                        []byte("&#10702;"),
	"ruluhar":                         []byte("&#10600;"),
	"sacute":                          []byte("&#347;"),
	"scaron":                          []byte("&#353;"),
	"scedil":                          []byte("&#351;"),
	"scirc":                           []byte("&#349;"),
	"scnsim":                          []byte("&#8937;"),
	"scpolint":                        []byte("&#10771;"),
	"searrow":                         []byte("&#8600;"),
	"semi":                            []byte(";"),
	"seswar":                          []byte("&tosa;"),
	"setminus":                        []byte("&#8726;"),
	"sfrown":                          []byte("&#8994;"),
	"shchcy":                          []byte("&#1097;"),
	"shortmid":                        []byte("&mid;"),
	"shortparallel":                   []byte("&par;"),
	"sigma":                           []byte("&#963;"),
	"sigmaf":                          []byte("&#962;"),
	"sigmav":                          []byte("&#962;"),
	"simeq":                           []byte("&sime;"),
	"simplus":                         []byte("&#10788;"),
	"simrarr":                         []byte("&#10610;"),
	"slarr":                           []byte("&larr;"),
	"smallsetminus":                   []byte("&#8726;"),
	"smeparsl":                        []byte("&#10724;"),
	"smid":                            []byte("&mid;"),
	"softcy":                          []byte("&#1100;"),
	"sol":                             []byte("/"),
	"solbar":                          []byte("&#9023;"),
	"spades":                          []byte("&#9824;"),
	"spadesuit":                       []byte("&#9824;"),
	"spar":                            []byte("&par;"),
	"sqsube":                          []byte("&#8849;"),
	"sqsubset":                        []byte("&#8847;"),
	"sqsubseteq":                      []byte("&#8849;"),
	"sqsupe":                          []byte("&#8850;"),
	"sqsupset":                        []byte("&#8848;"),
	"sqsupseteq":                      []byte("&#8850;"),
	"square":                          []byte("&squ;"),
	"squarf":                          []byte("&squf;"),
	"srarr":                           []byte("&rarr;"),
	"ssetmn":                          []byte("&#8726;"),
	"ssmile":                          []byte("&#8995;"),
	"sstarf":                          []byte("&Star;"),
	"straightepsilon":                 []byte("&#1013;"),
	"straightphi":                     []byte("&#981;"),
	"strns":                           []byte("&#175;"),
	"subedot":                         []byte("&#10947;"),
	"submult":                         []byte("&#10945;"),
	"subplus":                         []byte("&#10943;"),
	"subrarr":                         []byte("&#10617;"),
	"subset":                          []byte("&sub;"),
	"subseteq":                        []byte("&sube;"),
	"subseteqq":                       []byte("&subE;"),
	"subsetneq":                       []byte("&#8842;"),
	"subsetneqq":                      []byte("&subnE;"),
	"succ":                            []byte("&sc;"),
	"succapprox":                      []byte("&scap;"),
	"succcurlyeq":                     []byte("&#8829;"),
	"succeq":                          []byte("&sce;"),
	"succnapprox":                     []byte("&scnap;"),
	"succneqq":                        []byte("&scnE;"),
	"succnsim":                        []byte("&#8937;"),
	"succsim":                         []byte("&#8831;"),
	"supdsub":                         []byte("&#10968;"),
	"supedot":                         []byte("&#10948;"),
	"suphsol":                         []byte("&#10185;"),
	"suphsub":                         []byte("&#10967;"),
	"suplarr":                         []byte("&#10619;"),
	"supmult":                         []byte("&#10946;"),
	"supplus":                         []byte("&#10944;"),
	"supset":                          []byte("&sup;"),
	"supseteq":                        []byte("&supe;"),
	"supseteqq":                       []byte("&supE;"),
	"supsetneq":                       []byte("&#8843;"),
	"supsetneqq":                      []byte("&supnE;"),
	"swarrow":                         []byte("&#8601;"),
	"szlig":                           []byte("&#223;"),
	"target":                          []byte("&#8982;"),
	"tcaron":                          []byte("&#357;"),
	"tcedil":                          []byte("&#355;"),
	"telrec":                          []byte("&#8981;"),
	"there4":                          []byte("&#8756;"),
	"therefore":                       []byte("&#8756;"),
	"theta":                           []byte("&#952;"),
	"thetasym":                        []byte("&#977;"),
	"thetav":                          []byte("&#977;"),
	"thickapprox":                     []byte("&ap;"),
	"thicksim":                        []byte("&sim;"),
	"thinsp":                          []byte("&#8201;"),
	"thkap":                           []byte("&ap;"),
	"thksim":                          []byte("&sim;"),
	"thorn":                           []byte("&#254;"),
	"tilde":                           []byte("&#732;"),
	"times":                           []byte("&#215;"),
	"timesb":                          []byte("&#8864;"),
	"timesbar":                        []byte("&#10801;"),
	"topbot":                          []byte("&#9014;"),
	"topfork":                         []byte("&#10970;"),
	"tprime":                          []byte("&#8244;"),
	"triangle":                        []byte("&utri;"),
	"triangledown":                    []byte("&dtri;"),
	"triangleleft":                    []byte("&ltri;"),
	"trianglelefteq":                  []byte("&#8884;"),
	"triangleq":                       []byte("&trie;"),
	"triangleright":                   []byte("&rtri;"),
	"trianglerighteq":                 []byte("&#8885;"),
	"tridot":                          []byte("&#9708;"),
	"triminus":                        []byte("&#10810;"),
	"triplus":                         []byte("&#10809;"),
	"tritime":                         []byte("&#10811;"),
	"trpezium":                        []byte("&#9186;"),
	"tstrok":                          []byte("&#359;"),
	"twoheadleftarrow":                []byte("&Larr;"),
	"twoheadrightarrow":               []byte("&Rarr;"),
	"uacute":                          []byte("&#250;"),
	"ubreve":                          []byte("&#365;"),
	"ucirc":                           []byte("&#251;"),
	"udblac":                          []byte("&#369;"),
	"ugrave":                          []byte("&#249;"),
	"ulcorn":                          []byte("&#8988;"),
	"ulcorner":                        []byte("&#8988;"),
	"ulcrop":                          []byte("&#8975;"),
	"umacr":                           []byte("&#363;"),
	"uogon":                           []byte("&#371;"),
	"uparrow":                         []byte("&uarr;"),
	"updownarrow":                     []byte("&varr;"),
	"upharpoonleft":                   []byte("&#8639;"),
	"upharpoonright":                  []byte("&#8638;"),
	"upsih":                           []byte("&#978;"),
	"upsilon":                         []byte("&#965;"),
	"upuparrows":                      []byte("&#8648;"),
	"urcorn":                          []byte("&#8989;"),
	"urcorner":                        []byte("&#8989;"),
	"urcrop":                          []byte("&#8974;"),
	"uring":                           []byte("&#367;"),
	"utilde":                          []byte("&#361;"),
	"uwangle":                         []byte("&#10663;"),
	"varepsilon":                      []byte("&#1013;"),
	"varkappa":                        []byte("&#1008;"),
	"varnothing":                      []byte("&#8709;"),
	"varphi":                          []byte("&#981;"),
	"varpi":                           []byte("&piv;"),
	"varpropto":                       []byte("&prop;"),
	"varrho":                          []byte("&rhov;"),
	"varsigma":                        []byte("&#962;"),
	"vartheta":                        []byte("&#977;"),
	"vartriangleleft":                 []byte("&#8882;"),
	"vartriangleright":                []byte("&#8883;"),
	"vee":                             []byte("&or;"),
	"veebar":                          []byte("&#8891;"),
	"vellip":                          []byte("&#8942;"),
	"verbar":                          []byte("|"),
	"vert":                            []byte("|"),
	"vprop":                           []byte("&prop;"),
	"vzigzag":                         []byte("&#10650;"),
	"wcirc":                           []byte("&#373;"),
	"wedge":                           []byte("&and;"),
	"wedgeq":                          []byte("&#8793;"),
	"weierp":                          []byte("&wp;"),
	"wreath":                          []byte("&wr;"),
	"xvee":                            []byte("&Vee;"),
	"xwedge":                          []byte("&#8896;"),
	"yacute":                          []byte("&#253;"),
	"ycirc":                           []byte("&#375;"),
	"zacute":                          []byte("&#378;"),
	"zcaron":                          []byte("&#382;"),
	"zeetrf":                          []byte("&Zfr;"),
	"zigrarr":                         []byte("&#8669;"),
}

// TextRevEntitiesMap is a map of escapes.
var TextRevEntitiesMap = map[byte][]byte{
	'<': []byte("&lt;"),
}
