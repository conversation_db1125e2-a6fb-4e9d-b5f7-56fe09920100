// Code generated by smithy-go-codegen DO NOT EDIT.

// Package sso provides the API client, operations, and parameter types for AWS
// Single Sign-On.
//
// AWS IAM Identity Center (successor to AWS Single Sign-On) Portal is a web
// service that makes it easy for you to assign user access to IAM Identity Center
// resources such as the AWS access portal. Users can get AWS account applications
// and roles assigned to them and get federated into the application.
//
// Although AWS Single Sign-On was renamed, the sso and identitystore API
// namespaces will continue to retain their original name for backward
// compatibility purposes. For more information, see [IAM Identity Center rename].
//
// This reference guide describes the IAM Identity Center Portal operations that
// you can call programatically and includes detailed information on data types and
// errors.
//
// AWS provides SDKs that consist of libraries and sample code for various
// programming languages and platforms, such as Java, Ruby, .Net, iOS, or Android.
// The SDKs provide a convenient way to create programmatic access to IAM Identity
// Center and other AWS services. For more information about the AWS SDKs,
// including how to download and install them, see [Tools for Amazon Web Services].
//
// [Tools for Amazon Web Services]: http://aws.amazon.com/tools/
// [IAM Identity Center rename]: https://docs.aws.amazon.com/singlesignon/latest/userguide/what-is.html#renamed
package sso
