// Code generated by smithy-go-codegen DO NOT EDIT.

package types

import (
	"fmt"
	smithy "github.com/aws/smithy-go"
)

// The request was rejected because it attempted to create a resource that already
// exists.
type AlreadyExistsException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *AlreadyExistsException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *AlreadyExistsException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *AlreadyExistsException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "AlreadyExistsException"
	}
	return *e.ErrorCodeOverride
}
func (e *AlreadyExistsException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the specified CloudHSM cluster is already
// associated with an CloudHSM key store in the account, or it shares a backup
// history with an CloudHSM key store in the account. Each CloudHSM key store in
// the account must be associated with a different CloudHSM cluster.
//
// CloudHSM clusters that share a backup history have the same cluster
// certificate. To view the cluster certificate of an CloudHSM cluster, use the [DescribeClusters]
// operation.
//
// [DescribeClusters]: https://docs.aws.amazon.com/cloudhsm/latest/APIReference/API_DescribeClusters.html
type CloudHsmClusterInUseException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *CloudHsmClusterInUseException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *CloudHsmClusterInUseException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *CloudHsmClusterInUseException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "CloudHsmClusterInUseException"
	}
	return *e.ErrorCodeOverride
}
func (e *CloudHsmClusterInUseException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the associated CloudHSM cluster did not meet
// the configuration requirements for an CloudHSM key store.
//
//   - The CloudHSM cluster must be configured with private subnets in at least
//     two different Availability Zones in the Region.
//
//   - The [security group for the cluster](cloudhsm-cluster--sg) must include inbound rules and outbound rules
//     that allow TCP traffic on ports 2223-2225. The Source in the inbound rules and
//     the Destination in the outbound rules must match the security group ID. These
//     rules are set by default when you create the CloudHSM cluster. Do not delete or
//     change them. To get information about a particular security group, use the [DescribeSecurityGroups]
//     operation.
//
//   - The CloudHSM cluster must contain at least as many HSMs as the operation
//     requires. To add HSMs, use the CloudHSM [CreateHsm]operation.
//
// For the CreateCustomKeyStore, UpdateCustomKeyStore, and CreateKeyoperations, the CloudHSM cluster must have at least two active
//
//	HSMs, each in a different Availability Zone. For the ConnectCustomKeyStoreoperation, the CloudHSM
//	must contain at least one active HSM.
//
// For information about the requirements for an CloudHSM cluster that is
// associated with an CloudHSM key store, see [Assemble the Prerequisites]in the Key Management Service
// Developer Guide. For information about creating a private subnet for an CloudHSM
// cluster, see [Create a Private Subnet]in the CloudHSM User Guide. For information about cluster security
// groups, see [Configure a Default Security Group]in the CloudHSM User Guide .
//
// [Assemble the Prerequisites]: https://docs.aws.amazon.com/kms/latest/developerguide/create-keystore.html#before-keystore
// [Create a Private Subnet]: https://docs.aws.amazon.com/cloudhsm/latest/userguide/create-subnets.html
// [Configure a Default Security Group]: https://docs.aws.amazon.com/cloudhsm/latest/userguide/configure-sg.html
// [DescribeSecurityGroups]: https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_DescribeSecurityGroups.html
// [CreateHsm]: https://docs.aws.amazon.com/cloudhsm/latest/APIReference/API_CreateHsm.html
// [security group for the cluster]: https://docs.aws.amazon.com/cloudhsm/latest/userguide/configure-sg.html
type CloudHsmClusterInvalidConfigurationException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *CloudHsmClusterInvalidConfigurationException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *CloudHsmClusterInvalidConfigurationException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *CloudHsmClusterInvalidConfigurationException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "CloudHsmClusterInvalidConfigurationException"
	}
	return *e.ErrorCodeOverride
}
func (e *CloudHsmClusterInvalidConfigurationException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The request was rejected because the CloudHSM cluster associated with the
// CloudHSM key store is not active. Initialize and activate the cluster and try
// the command again. For detailed instructions, see [Getting Started]in the CloudHSM User Guide.
//
// [Getting Started]: https://docs.aws.amazon.com/cloudhsm/latest/userguide/getting-started.html
type CloudHsmClusterNotActiveException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *CloudHsmClusterNotActiveException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *CloudHsmClusterNotActiveException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *CloudHsmClusterNotActiveException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "CloudHsmClusterNotActiveException"
	}
	return *e.ErrorCodeOverride
}
func (e *CloudHsmClusterNotActiveException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because KMS cannot find the CloudHSM cluster with the
// specified cluster ID. Retry the request with a different cluster ID.
type CloudHsmClusterNotFoundException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *CloudHsmClusterNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *CloudHsmClusterNotFoundException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *CloudHsmClusterNotFoundException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "CloudHsmClusterNotFoundException"
	}
	return *e.ErrorCodeOverride
}
func (e *CloudHsmClusterNotFoundException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the specified CloudHSM cluster has a different
// cluster certificate than the original cluster. You cannot use the operation to
// specify an unrelated cluster for an CloudHSM key store.
//
// Specify an CloudHSM cluster that shares a backup history with the original
// cluster. This includes clusters that were created from a backup of the current
// cluster, and clusters that were created from the same backup that produced the
// current cluster.
//
// CloudHSM clusters that share a backup history have the same cluster
// certificate. To view the cluster certificate of an CloudHSM cluster, use the [DescribeClusters]
// operation.
//
// [DescribeClusters]: https://docs.aws.amazon.com/cloudhsm/latest/APIReference/API_DescribeClusters.html
type CloudHsmClusterNotRelatedException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *CloudHsmClusterNotRelatedException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *CloudHsmClusterNotRelatedException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *CloudHsmClusterNotRelatedException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "CloudHsmClusterNotRelatedException"
	}
	return *e.ErrorCodeOverride
}
func (e *CloudHsmClusterNotRelatedException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The request was rejected because an automatic rotation of this key is currently
// in progress or scheduled to begin within the next 20 minutes.
type ConflictException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *ConflictException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ConflictException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ConflictException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "ConflictException"
	}
	return *e.ErrorCodeOverride
}
func (e *ConflictException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the custom key store contains KMS keys. After
// verifying that you do not need to use the KMS keys, use the ScheduleKeyDeletionoperation to delete
// the KMS keys. After they are deleted, you can delete the custom key store.
type CustomKeyStoreHasCMKsException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *CustomKeyStoreHasCMKsException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *CustomKeyStoreHasCMKsException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *CustomKeyStoreHasCMKsException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "CustomKeyStoreHasCMKsException"
	}
	return *e.ErrorCodeOverride
}
func (e *CustomKeyStoreHasCMKsException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because of the ConnectionState of the custom key
// store. To get the ConnectionState of a custom key store, use the DescribeCustomKeyStores operation.
//
// This exception is thrown under the following conditions:
//
//   - You requested the ConnectCustomKeyStoreoperation on a custom key store with a ConnectionState of
//     DISCONNECTING or FAILED . This operation is valid for all other
//     ConnectionState values. To reconnect a custom key store in a FAILED state,
//     disconnect it (DisconnectCustomKeyStore ), then connect it ( ConnectCustomKeyStore ).
//
//   - You requested the CreateKeyoperation in a custom key store that is not connected.
//     This operations is valid only when the custom key store ConnectionState is
//     CONNECTED .
//
//   - You requested the DisconnectCustomKeyStoreoperation on a custom key store with a ConnectionState of
//     DISCONNECTING or DISCONNECTED . This operation is valid for all other
//     ConnectionState values.
//
//   - You requested the UpdateCustomKeyStoreor DeleteCustomKeyStoreoperation on a custom key store that is not
//     disconnected. This operation is valid only when the custom key store
//     ConnectionState is DISCONNECTED .
//
//   - You requested the GenerateRandomoperation in an CloudHSM key store that is not connected.
//     This operation is valid only when the CloudHSM key store ConnectionState is
//     CONNECTED .
type CustomKeyStoreInvalidStateException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *CustomKeyStoreInvalidStateException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *CustomKeyStoreInvalidStateException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *CustomKeyStoreInvalidStateException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "CustomKeyStoreInvalidStateException"
	}
	return *e.ErrorCodeOverride
}
func (e *CustomKeyStoreInvalidStateException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The request was rejected because the specified custom key store name is already
// assigned to another custom key store in the account. Try again with a custom key
// store name that is unique in the account.
type CustomKeyStoreNameInUseException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *CustomKeyStoreNameInUseException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *CustomKeyStoreNameInUseException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *CustomKeyStoreNameInUseException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "CustomKeyStoreNameInUseException"
	}
	return *e.ErrorCodeOverride
}
func (e *CustomKeyStoreNameInUseException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because KMS cannot find a custom key store with the
// specified key store name or ID.
type CustomKeyStoreNotFoundException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *CustomKeyStoreNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *CustomKeyStoreNotFoundException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *CustomKeyStoreNotFoundException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "CustomKeyStoreNotFoundException"
	}
	return *e.ErrorCodeOverride
}
func (e *CustomKeyStoreNotFoundException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The system timed out while trying to fulfill the request. You can retry the
// request.
type DependencyTimeoutException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *DependencyTimeoutException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *DependencyTimeoutException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *DependencyTimeoutException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "DependencyTimeoutException"
	}
	return *e.ErrorCodeOverride
}
func (e *DependencyTimeoutException) ErrorFault() smithy.ErrorFault { return smithy.FaultServer }

// The request was rejected because the specified KMS key is not enabled.
type DisabledException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *DisabledException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *DisabledException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *DisabledException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "DisabledException"
	}
	return *e.ErrorCodeOverride
}
func (e *DisabledException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the DryRun parameter was specified.
type DryRunOperationException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *DryRunOperationException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *DryRunOperationException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *DryRunOperationException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "DryRunOperationException"
	}
	return *e.ErrorCodeOverride
}
func (e *DryRunOperationException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the specified import token is expired. Use GetParametersForImport to
// get a new import token and public key, use the new public key to encrypt the key
// material, and then try the request again.
type ExpiredImportTokenException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *ExpiredImportTokenException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ExpiredImportTokenException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ExpiredImportTokenException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "ExpiredImportTokenException"
	}
	return *e.ErrorCodeOverride
}
func (e *ExpiredImportTokenException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the specified KMS key cannot decrypt the data.
// The KeyId in a Decrypt request and the SourceKeyId in a ReEncrypt request must identify the
// same KMS key that was used to encrypt the ciphertext.
type IncorrectKeyException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *IncorrectKeyException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *IncorrectKeyException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *IncorrectKeyException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "IncorrectKeyException"
	}
	return *e.ErrorCodeOverride
}
func (e *IncorrectKeyException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the key material in the request is, expired,
// invalid, or is not the same key material that was previously imported into this
// KMS key.
type IncorrectKeyMaterialException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *IncorrectKeyMaterialException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *IncorrectKeyMaterialException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *IncorrectKeyMaterialException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "IncorrectKeyMaterialException"
	}
	return *e.ErrorCodeOverride
}
func (e *IncorrectKeyMaterialException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the trust anchor certificate in the request to
// create an CloudHSM key store is not the trust anchor certificate for the
// specified CloudHSM cluster.
//
// When you [initialize the CloudHSM cluster], you create the trust anchor certificate and save it in the
// customerCA.crt file.
//
// [initialize the CloudHSM cluster]: https://docs.aws.amazon.com/cloudhsm/latest/userguide/initialize-cluster.html#sign-csr
type IncorrectTrustAnchorException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *IncorrectTrustAnchorException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *IncorrectTrustAnchorException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *IncorrectTrustAnchorException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "IncorrectTrustAnchorException"
	}
	return *e.ErrorCodeOverride
}
func (e *IncorrectTrustAnchorException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the specified alias name is not valid.
type InvalidAliasNameException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *InvalidAliasNameException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidAliasNameException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidAliasNameException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "InvalidAliasNameException"
	}
	return *e.ErrorCodeOverride
}
func (e *InvalidAliasNameException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because a specified ARN, or an ARN in a key policy, is
// not valid.
type InvalidArnException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *InvalidArnException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidArnException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidArnException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "InvalidArnException"
	}
	return *e.ErrorCodeOverride
}
func (e *InvalidArnException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// From the Decrypt or ReEncrypt operation, the request was rejected because the specified
// ciphertext, or additional authenticated data incorporated into the ciphertext,
// such as the encryption context, is corrupted, missing, or otherwise invalid.
//
// From the ImportKeyMaterial operation, the request was rejected because KMS could not decrypt the
// encrypted (wrapped) key material.
type InvalidCiphertextException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *InvalidCiphertextException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidCiphertextException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidCiphertextException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "InvalidCiphertextException"
	}
	return *e.ErrorCodeOverride
}
func (e *InvalidCiphertextException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the specified GrantId is not valid.
type InvalidGrantIdException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *InvalidGrantIdException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidGrantIdException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidGrantIdException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "InvalidGrantIdException"
	}
	return *e.ErrorCodeOverride
}
func (e *InvalidGrantIdException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the specified grant token is not valid.
type InvalidGrantTokenException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *InvalidGrantTokenException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidGrantTokenException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidGrantTokenException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "InvalidGrantTokenException"
	}
	return *e.ErrorCodeOverride
}
func (e *InvalidGrantTokenException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the provided import token is invalid or is
// associated with a different KMS key.
type InvalidImportTokenException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *InvalidImportTokenException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidImportTokenException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidImportTokenException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "InvalidImportTokenException"
	}
	return *e.ErrorCodeOverride
}
func (e *InvalidImportTokenException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected for one of the following reasons:
//
//   - The KeyUsage value of the KMS key is incompatible with the API operation.
//
//   - The encryption algorithm or signing algorithm specified for the operation
//     is incompatible with the type of key material in the KMS key (KeySpec ).
//
// For encrypting, decrypting, re-encrypting, and generating data keys, the
// KeyUsage must be ENCRYPT_DECRYPT . For signing and verifying messages, the
// KeyUsage must be SIGN_VERIFY . For generating and verifying message
// authentication codes (MACs), the KeyUsage must be GENERATE_VERIFY_MAC . For
// deriving key agreement secrets, the KeyUsage must be KEY_AGREEMENT . To find the
// KeyUsage of a KMS key, use the DescribeKey operation.
//
// To find the encryption or signing algorithms supported for a particular KMS
// key, use the DescribeKeyoperation.
type InvalidKeyUsageException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *InvalidKeyUsageException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidKeyUsageException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidKeyUsageException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "InvalidKeyUsageException"
	}
	return *e.ErrorCodeOverride
}
func (e *InvalidKeyUsageException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the marker that specifies where pagination
// should next begin is not valid.
type InvalidMarkerException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *InvalidMarkerException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidMarkerException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidMarkerException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "InvalidMarkerException"
	}
	return *e.ErrorCodeOverride
}
func (e *InvalidMarkerException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the specified KMS key was not available. You
// can retry the request.
type KeyUnavailableException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *KeyUnavailableException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *KeyUnavailableException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *KeyUnavailableException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "KeyUnavailableException"
	}
	return *e.ErrorCodeOverride
}
func (e *KeyUnavailableException) ErrorFault() smithy.ErrorFault { return smithy.FaultServer }

// The request was rejected because an internal exception occurred. The request
// can be retried.
type KMSInternalException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *KMSInternalException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *KMSInternalException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *KMSInternalException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "KMSInternalException"
	}
	return *e.ErrorCodeOverride
}
func (e *KMSInternalException) ErrorFault() smithy.ErrorFault { return smithy.FaultServer }

// The request was rejected because the HMAC verification failed. HMAC
// verification fails when the HMAC computed by using the specified message, HMAC
// KMS key, and MAC algorithm does not match the HMAC specified in the request.
type KMSInvalidMacException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *KMSInvalidMacException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *KMSInvalidMacException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *KMSInvalidMacException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "KMSInvalidMacException"
	}
	return *e.ErrorCodeOverride
}
func (e *KMSInvalidMacException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the signature verification failed. Signature
// verification fails when it cannot confirm that signature was produced by signing
// the specified message with the specified KMS key and signing algorithm.
type KMSInvalidSignatureException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *KMSInvalidSignatureException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *KMSInvalidSignatureException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *KMSInvalidSignatureException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "KMSInvalidSignatureException"
	}
	return *e.ErrorCodeOverride
}
func (e *KMSInvalidSignatureException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the state of the specified resource is not
// valid for this request.
//
// This exceptions means one of the following:
//
//   - The key state of the KMS key is not compatible with the operation.
//
// To find the key state, use the DescribeKeyoperation. For more information about which key
//
//	states are compatible with each KMS operation, see [Key states of KMS keys]in the Key Management
//	Service Developer Guide .
//
//	- For cryptographic operations on KMS keys in custom key stores, this
//	exception represents a general failure with many possible causes. To identify
//	the cause, see the error message that accompanies the exception.
//
// [Key states of KMS keys]: https://docs.aws.amazon.com/kms/latest/developerguide/key-state.html
type KMSInvalidStateException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *KMSInvalidStateException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *KMSInvalidStateException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *KMSInvalidStateException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "KMSInvalidStateException"
	}
	return *e.ErrorCodeOverride
}
func (e *KMSInvalidStateException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because a quota was exceeded. For more information,
// see [Quotas]in the Key Management Service Developer Guide.
//
// [Quotas]: https://docs.aws.amazon.com/kms/latest/developerguide/limits.html
type LimitExceededException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *LimitExceededException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *LimitExceededException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *LimitExceededException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "LimitExceededException"
	}
	return *e.ErrorCodeOverride
}
func (e *LimitExceededException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the specified policy is not syntactically or
// semantically correct.
type MalformedPolicyDocumentException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *MalformedPolicyDocumentException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *MalformedPolicyDocumentException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *MalformedPolicyDocumentException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "MalformedPolicyDocumentException"
	}
	return *e.ErrorCodeOverride
}
func (e *MalformedPolicyDocumentException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the specified entity or resource could not be
// found.
type NotFoundException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *NotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *NotFoundException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *NotFoundException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "NotFoundException"
	}
	return *e.ErrorCodeOverride
}
func (e *NotFoundException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because one or more tags are not valid.
type TagException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *TagException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *TagException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *TagException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "TagException"
	}
	return *e.ErrorCodeOverride
}
func (e *TagException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because a specified parameter is not supported or a
// specified resource is not valid for this operation.
type UnsupportedOperationException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *UnsupportedOperationException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *UnsupportedOperationException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *UnsupportedOperationException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "UnsupportedOperationException"
	}
	return *e.ErrorCodeOverride
}
func (e *UnsupportedOperationException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the ( XksKeyId ) is already associated with
// another KMS key in this external key store. Each KMS key in an external key
// store must be associated with a different external key.
type XksKeyAlreadyInUseException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *XksKeyAlreadyInUseException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *XksKeyAlreadyInUseException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *XksKeyAlreadyInUseException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "XksKeyAlreadyInUseException"
	}
	return *e.ErrorCodeOverride
}
func (e *XksKeyAlreadyInUseException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the external key specified by the XksKeyId
// parameter did not meet the configuration requirements for an external key store.
//
// The external key must be an AES-256 symmetric key that is enabled and performs
// encryption and decryption.
type XksKeyInvalidConfigurationException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *XksKeyInvalidConfigurationException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *XksKeyInvalidConfigurationException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *XksKeyInvalidConfigurationException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "XksKeyInvalidConfigurationException"
	}
	return *e.ErrorCodeOverride
}
func (e *XksKeyInvalidConfigurationException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The request was rejected because the external key store proxy could not find
// the external key. This exception is thrown when the value of the XksKeyId
// parameter doesn't identify a key in the external key manager associated with the
// external key proxy.
//
// Verify that the XksKeyId represents an existing key in the external key
// manager. Use the key identifier that the external key store proxy uses to
// identify the key. For details, see the documentation provided with your external
// key store proxy or key manager.
type XksKeyNotFoundException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *XksKeyNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *XksKeyNotFoundException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *XksKeyNotFoundException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "XksKeyNotFoundException"
	}
	return *e.ErrorCodeOverride
}
func (e *XksKeyNotFoundException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the proxy credentials failed to authenticate
// to the specified external key store proxy. The specified external key store
// proxy rejected a status request from KMS due to invalid credentials. This can
// indicate an error in the credentials or in the identification of the external
// key store proxy.
type XksProxyIncorrectAuthenticationCredentialException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *XksProxyIncorrectAuthenticationCredentialException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *XksProxyIncorrectAuthenticationCredentialException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *XksProxyIncorrectAuthenticationCredentialException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "XksProxyIncorrectAuthenticationCredentialException"
	}
	return *e.ErrorCodeOverride
}
func (e *XksProxyIncorrectAuthenticationCredentialException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The request was rejected because the external key store proxy is not configured
// correctly. To identify the cause, see the error message that accompanies the
// exception.
type XksProxyInvalidConfigurationException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *XksProxyInvalidConfigurationException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *XksProxyInvalidConfigurationException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *XksProxyInvalidConfigurationException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "XksProxyInvalidConfigurationException"
	}
	return *e.ErrorCodeOverride
}
func (e *XksProxyInvalidConfigurationException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// KMS cannot interpret the response it received from the external key store
// proxy. The problem might be a poorly constructed response, but it could also be
// a transient network issue. If you see this error repeatedly, report it to the
// proxy vendor.
type XksProxyInvalidResponseException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *XksProxyInvalidResponseException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *XksProxyInvalidResponseException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *XksProxyInvalidResponseException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "XksProxyInvalidResponseException"
	}
	return *e.ErrorCodeOverride
}
func (e *XksProxyInvalidResponseException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the XksProxyUriEndpoint is already associated
// with another external key store in this Amazon Web Services Region. To identify
// the cause, see the error message that accompanies the exception.
type XksProxyUriEndpointInUseException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *XksProxyUriEndpointInUseException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *XksProxyUriEndpointInUseException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *XksProxyUriEndpointInUseException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "XksProxyUriEndpointInUseException"
	}
	return *e.ErrorCodeOverride
}
func (e *XksProxyUriEndpointInUseException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the concatenation of the XksProxyUriEndpoint
// and XksProxyUriPath is already associated with another external key store in
// this Amazon Web Services Region. Each external key store in a Region must use a
// unique external key store proxy API address.
type XksProxyUriInUseException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *XksProxyUriInUseException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *XksProxyUriInUseException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *XksProxyUriInUseException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "XksProxyUriInUseException"
	}
	return *e.ErrorCodeOverride
}
func (e *XksProxyUriInUseException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// KMS was unable to reach the specified XksProxyUriPath . The path must be
// reachable before you create the external key store or update its settings.
//
// This exception is also thrown when the external key store proxy response to a
// GetHealthStatus request indicates that all external key manager instances are
// unavailable.
type XksProxyUriUnreachableException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *XksProxyUriUnreachableException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *XksProxyUriUnreachableException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *XksProxyUriUnreachableException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "XksProxyUriUnreachableException"
	}
	return *e.ErrorCodeOverride
}
func (e *XksProxyUriUnreachableException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request was rejected because the specified Amazon VPC endpoint service is
// already associated with another external key store in this Amazon Web Services
// Region. Each external key store in a Region must use a different Amazon VPC
// endpoint service.
type XksProxyVpcEndpointServiceInUseException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *XksProxyVpcEndpointServiceInUseException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *XksProxyVpcEndpointServiceInUseException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *XksProxyVpcEndpointServiceInUseException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "XksProxyVpcEndpointServiceInUseException"
	}
	return *e.ErrorCodeOverride
}
func (e *XksProxyVpcEndpointServiceInUseException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The request was rejected because the Amazon VPC endpoint service configuration
// does not fulfill the requirements for an external key store. To identify the
// cause, see the error message that accompanies the exception and [review the requirements]for Amazon VPC
// endpoint service connectivity for an external key store.
//
// [review the requirements]: https://docs.aws.amazon.com/kms/latest/developerguide/vpc-connectivity.html#xks-vpc-requirements
type XksProxyVpcEndpointServiceInvalidConfigurationException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *XksProxyVpcEndpointServiceInvalidConfigurationException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *XksProxyVpcEndpointServiceInvalidConfigurationException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *XksProxyVpcEndpointServiceInvalidConfigurationException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "XksProxyVpcEndpointServiceInvalidConfigurationException"
	}
	return *e.ErrorCodeOverride
}
func (e *XksProxyVpcEndpointServiceInvalidConfigurationException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The request was rejected because KMS could not find the specified VPC endpoint
// service. Use DescribeCustomKeyStoresto verify the VPC endpoint service name for the external key
// store. Also, confirm that the Allow principals list for the VPC endpoint
// service includes the KMS service principal for the Region, such as
// cks.kms.us-east-1.amazonaws.com .
type XksProxyVpcEndpointServiceNotFoundException struct {
	Message *string

	ErrorCodeOverride *string

	noSmithyDocumentSerde
}

func (e *XksProxyVpcEndpointServiceNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *XksProxyVpcEndpointServiceNotFoundException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *XksProxyVpcEndpointServiceNotFoundException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "XksProxyVpcEndpointServiceNotFoundException"
	}
	return *e.ErrorCodeOverride
}
func (e *XksProxyVpcEndpointServiceNotFoundException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}
