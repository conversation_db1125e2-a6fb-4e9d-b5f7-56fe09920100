// Code generated by smithy-go-codegen DO NOT EDIT.

package kms

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws/protocol/restjson"
	"github.com/aws/aws-sdk-go-v2/service/kms/types"
	smithy "github.com/aws/smithy-go"
	smithyio "github.com/aws/smithy-go/io"
	"github.com/aws/smithy-go/middleware"
	"github.com/aws/smithy-go/ptr"
	smithytime "github.com/aws/smithy-go/time"
	"github.com/aws/smithy-go/tracing"
	smithyhttp "github.com/aws/smithy-go/transport/http"
	"io"
	"io/ioutil"
	"strings"
	"time"
)

func deserializeS3Expires(v string) (*time.Time, error) {
	t, err := smithytime.ParseHTTPDate(v)
	if err != nil {
		return nil, nil
	}
	return &t, nil
}

type awsAwsjson11_deserializeOpCancelKeyDeletion struct {
}

func (*awsAwsjson11_deserializeOpCancelKeyDeletion) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpCancelKeyDeletion) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorCancelKeyDeletion(response, &metadata)
	}
	output := &CancelKeyDeletionOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentCancelKeyDeletionOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorCancelKeyDeletion(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpConnectCustomKeyStore struct {
}

func (*awsAwsjson11_deserializeOpConnectCustomKeyStore) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpConnectCustomKeyStore) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorConnectCustomKeyStore(response, &metadata)
	}
	output := &ConnectCustomKeyStoreOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentConnectCustomKeyStoreOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorConnectCustomKeyStore(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("CloudHsmClusterInvalidConfigurationException", errorCode):
		return awsAwsjson11_deserializeErrorCloudHsmClusterInvalidConfigurationException(response, errorBody)

	case strings.EqualFold("CloudHsmClusterNotActiveException", errorCode):
		return awsAwsjson11_deserializeErrorCloudHsmClusterNotActiveException(response, errorBody)

	case strings.EqualFold("CustomKeyStoreInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorCustomKeyStoreInvalidStateException(response, errorBody)

	case strings.EqualFold("CustomKeyStoreNotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorCustomKeyStoreNotFoundException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpCreateAlias struct {
}

func (*awsAwsjson11_deserializeOpCreateAlias) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpCreateAlias) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorCreateAlias(response, &metadata)
	}
	output := &CreateAliasOutput{}
	out.Result = output

	if _, err = io.Copy(ioutil.Discard, response.Body); err != nil {
		return out, metadata, &smithy.DeserializationError{
			Err: fmt.Errorf("failed to discard response body, %w", err),
		}
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorCreateAlias(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("AlreadyExistsException", errorCode):
		return awsAwsjson11_deserializeErrorAlreadyExistsException(response, errorBody)

	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidAliasNameException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidAliasNameException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("LimitExceededException", errorCode):
		return awsAwsjson11_deserializeErrorLimitExceededException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpCreateCustomKeyStore struct {
}

func (*awsAwsjson11_deserializeOpCreateCustomKeyStore) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpCreateCustomKeyStore) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorCreateCustomKeyStore(response, &metadata)
	}
	output := &CreateCustomKeyStoreOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentCreateCustomKeyStoreOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorCreateCustomKeyStore(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("CloudHsmClusterInUseException", errorCode):
		return awsAwsjson11_deserializeErrorCloudHsmClusterInUseException(response, errorBody)

	case strings.EqualFold("CloudHsmClusterInvalidConfigurationException", errorCode):
		return awsAwsjson11_deserializeErrorCloudHsmClusterInvalidConfigurationException(response, errorBody)

	case strings.EqualFold("CloudHsmClusterNotActiveException", errorCode):
		return awsAwsjson11_deserializeErrorCloudHsmClusterNotActiveException(response, errorBody)

	case strings.EqualFold("CloudHsmClusterNotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorCloudHsmClusterNotFoundException(response, errorBody)

	case strings.EqualFold("CustomKeyStoreNameInUseException", errorCode):
		return awsAwsjson11_deserializeErrorCustomKeyStoreNameInUseException(response, errorBody)

	case strings.EqualFold("IncorrectTrustAnchorException", errorCode):
		return awsAwsjson11_deserializeErrorIncorrectTrustAnchorException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("LimitExceededException", errorCode):
		return awsAwsjson11_deserializeErrorLimitExceededException(response, errorBody)

	case strings.EqualFold("XksProxyIncorrectAuthenticationCredentialException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyIncorrectAuthenticationCredentialException(response, errorBody)

	case strings.EqualFold("XksProxyInvalidConfigurationException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyInvalidConfigurationException(response, errorBody)

	case strings.EqualFold("XksProxyInvalidResponseException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyInvalidResponseException(response, errorBody)

	case strings.EqualFold("XksProxyUriEndpointInUseException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyUriEndpointInUseException(response, errorBody)

	case strings.EqualFold("XksProxyUriInUseException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyUriInUseException(response, errorBody)

	case strings.EqualFold("XksProxyUriUnreachableException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyUriUnreachableException(response, errorBody)

	case strings.EqualFold("XksProxyVpcEndpointServiceInUseException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyVpcEndpointServiceInUseException(response, errorBody)

	case strings.EqualFold("XksProxyVpcEndpointServiceInvalidConfigurationException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyVpcEndpointServiceInvalidConfigurationException(response, errorBody)

	case strings.EqualFold("XksProxyVpcEndpointServiceNotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyVpcEndpointServiceNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpCreateGrant struct {
}

func (*awsAwsjson11_deserializeOpCreateGrant) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpCreateGrant) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorCreateGrant(response, &metadata)
	}
	output := &CreateGrantOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentCreateGrantOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorCreateGrant(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("DryRunOperationException", errorCode):
		return awsAwsjson11_deserializeErrorDryRunOperationException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("InvalidGrantTokenException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantTokenException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("LimitExceededException", errorCode):
		return awsAwsjson11_deserializeErrorLimitExceededException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpCreateKey struct {
}

func (*awsAwsjson11_deserializeOpCreateKey) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpCreateKey) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorCreateKey(response, &metadata)
	}
	output := &CreateKeyOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentCreateKeyOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorCreateKey(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("CloudHsmClusterInvalidConfigurationException", errorCode):
		return awsAwsjson11_deserializeErrorCloudHsmClusterInvalidConfigurationException(response, errorBody)

	case strings.EqualFold("CustomKeyStoreInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorCustomKeyStoreInvalidStateException(response, errorBody)

	case strings.EqualFold("CustomKeyStoreNotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorCustomKeyStoreNotFoundException(response, errorBody)

	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("LimitExceededException", errorCode):
		return awsAwsjson11_deserializeErrorLimitExceededException(response, errorBody)

	case strings.EqualFold("MalformedPolicyDocumentException", errorCode):
		return awsAwsjson11_deserializeErrorMalformedPolicyDocumentException(response, errorBody)

	case strings.EqualFold("TagException", errorCode):
		return awsAwsjson11_deserializeErrorTagException(response, errorBody)

	case strings.EqualFold("UnsupportedOperationException", errorCode):
		return awsAwsjson11_deserializeErrorUnsupportedOperationException(response, errorBody)

	case strings.EqualFold("XksKeyAlreadyInUseException", errorCode):
		return awsAwsjson11_deserializeErrorXksKeyAlreadyInUseException(response, errorBody)

	case strings.EqualFold("XksKeyInvalidConfigurationException", errorCode):
		return awsAwsjson11_deserializeErrorXksKeyInvalidConfigurationException(response, errorBody)

	case strings.EqualFold("XksKeyNotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorXksKeyNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpDecrypt struct {
}

func (*awsAwsjson11_deserializeOpDecrypt) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpDecrypt) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorDecrypt(response, &metadata)
	}
	output := &DecryptOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentDecryptOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorDecrypt(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("DryRunOperationException", errorCode):
		return awsAwsjson11_deserializeErrorDryRunOperationException(response, errorBody)

	case strings.EqualFold("IncorrectKeyException", errorCode):
		return awsAwsjson11_deserializeErrorIncorrectKeyException(response, errorBody)

	case strings.EqualFold("InvalidCiphertextException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidCiphertextException(response, errorBody)

	case strings.EqualFold("InvalidGrantTokenException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantTokenException(response, errorBody)

	case strings.EqualFold("InvalidKeyUsageException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidKeyUsageException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("KeyUnavailableException", errorCode):
		return awsAwsjson11_deserializeErrorKeyUnavailableException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpDeleteAlias struct {
}

func (*awsAwsjson11_deserializeOpDeleteAlias) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpDeleteAlias) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorDeleteAlias(response, &metadata)
	}
	output := &DeleteAliasOutput{}
	out.Result = output

	if _, err = io.Copy(ioutil.Discard, response.Body); err != nil {
		return out, metadata, &smithy.DeserializationError{
			Err: fmt.Errorf("failed to discard response body, %w", err),
		}
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorDeleteAlias(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpDeleteCustomKeyStore struct {
}

func (*awsAwsjson11_deserializeOpDeleteCustomKeyStore) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpDeleteCustomKeyStore) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorDeleteCustomKeyStore(response, &metadata)
	}
	output := &DeleteCustomKeyStoreOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentDeleteCustomKeyStoreOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorDeleteCustomKeyStore(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("CustomKeyStoreHasCMKsException", errorCode):
		return awsAwsjson11_deserializeErrorCustomKeyStoreHasCMKsException(response, errorBody)

	case strings.EqualFold("CustomKeyStoreInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorCustomKeyStoreInvalidStateException(response, errorBody)

	case strings.EqualFold("CustomKeyStoreNotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorCustomKeyStoreNotFoundException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpDeleteImportedKeyMaterial struct {
}

func (*awsAwsjson11_deserializeOpDeleteImportedKeyMaterial) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpDeleteImportedKeyMaterial) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorDeleteImportedKeyMaterial(response, &metadata)
	}
	output := &DeleteImportedKeyMaterialOutput{}
	out.Result = output

	if _, err = io.Copy(ioutil.Discard, response.Body); err != nil {
		return out, metadata, &smithy.DeserializationError{
			Err: fmt.Errorf("failed to discard response body, %w", err),
		}
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorDeleteImportedKeyMaterial(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	case strings.EqualFold("UnsupportedOperationException", errorCode):
		return awsAwsjson11_deserializeErrorUnsupportedOperationException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpDeriveSharedSecret struct {
}

func (*awsAwsjson11_deserializeOpDeriveSharedSecret) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpDeriveSharedSecret) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorDeriveSharedSecret(response, &metadata)
	}
	output := &DeriveSharedSecretOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentDeriveSharedSecretOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorDeriveSharedSecret(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("DryRunOperationException", errorCode):
		return awsAwsjson11_deserializeErrorDryRunOperationException(response, errorBody)

	case strings.EqualFold("InvalidGrantTokenException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantTokenException(response, errorBody)

	case strings.EqualFold("InvalidKeyUsageException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidKeyUsageException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("KeyUnavailableException", errorCode):
		return awsAwsjson11_deserializeErrorKeyUnavailableException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpDescribeCustomKeyStores struct {
}

func (*awsAwsjson11_deserializeOpDescribeCustomKeyStores) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpDescribeCustomKeyStores) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorDescribeCustomKeyStores(response, &metadata)
	}
	output := &DescribeCustomKeyStoresOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentDescribeCustomKeyStoresOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorDescribeCustomKeyStores(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("CustomKeyStoreNotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorCustomKeyStoreNotFoundException(response, errorBody)

	case strings.EqualFold("InvalidMarkerException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidMarkerException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpDescribeKey struct {
}

func (*awsAwsjson11_deserializeOpDescribeKey) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpDescribeKey) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorDescribeKey(response, &metadata)
	}
	output := &DescribeKeyOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentDescribeKeyOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorDescribeKey(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpDisableKey struct {
}

func (*awsAwsjson11_deserializeOpDisableKey) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpDisableKey) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorDisableKey(response, &metadata)
	}
	output := &DisableKeyOutput{}
	out.Result = output

	if _, err = io.Copy(ioutil.Discard, response.Body); err != nil {
		return out, metadata, &smithy.DeserializationError{
			Err: fmt.Errorf("failed to discard response body, %w", err),
		}
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorDisableKey(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpDisableKeyRotation struct {
}

func (*awsAwsjson11_deserializeOpDisableKeyRotation) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpDisableKeyRotation) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorDisableKeyRotation(response, &metadata)
	}
	output := &DisableKeyRotationOutput{}
	out.Result = output

	if _, err = io.Copy(ioutil.Discard, response.Body); err != nil {
		return out, metadata, &smithy.DeserializationError{
			Err: fmt.Errorf("failed to discard response body, %w", err),
		}
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorDisableKeyRotation(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	case strings.EqualFold("UnsupportedOperationException", errorCode):
		return awsAwsjson11_deserializeErrorUnsupportedOperationException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpDisconnectCustomKeyStore struct {
}

func (*awsAwsjson11_deserializeOpDisconnectCustomKeyStore) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpDisconnectCustomKeyStore) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorDisconnectCustomKeyStore(response, &metadata)
	}
	output := &DisconnectCustomKeyStoreOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentDisconnectCustomKeyStoreOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorDisconnectCustomKeyStore(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("CustomKeyStoreInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorCustomKeyStoreInvalidStateException(response, errorBody)

	case strings.EqualFold("CustomKeyStoreNotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorCustomKeyStoreNotFoundException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpEnableKey struct {
}

func (*awsAwsjson11_deserializeOpEnableKey) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpEnableKey) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorEnableKey(response, &metadata)
	}
	output := &EnableKeyOutput{}
	out.Result = output

	if _, err = io.Copy(ioutil.Discard, response.Body); err != nil {
		return out, metadata, &smithy.DeserializationError{
			Err: fmt.Errorf("failed to discard response body, %w", err),
		}
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorEnableKey(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("LimitExceededException", errorCode):
		return awsAwsjson11_deserializeErrorLimitExceededException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpEnableKeyRotation struct {
}

func (*awsAwsjson11_deserializeOpEnableKeyRotation) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpEnableKeyRotation) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorEnableKeyRotation(response, &metadata)
	}
	output := &EnableKeyRotationOutput{}
	out.Result = output

	if _, err = io.Copy(ioutil.Discard, response.Body); err != nil {
		return out, metadata, &smithy.DeserializationError{
			Err: fmt.Errorf("failed to discard response body, %w", err),
		}
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorEnableKeyRotation(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	case strings.EqualFold("UnsupportedOperationException", errorCode):
		return awsAwsjson11_deserializeErrorUnsupportedOperationException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpEncrypt struct {
}

func (*awsAwsjson11_deserializeOpEncrypt) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpEncrypt) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorEncrypt(response, &metadata)
	}
	output := &EncryptOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentEncryptOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorEncrypt(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("DryRunOperationException", errorCode):
		return awsAwsjson11_deserializeErrorDryRunOperationException(response, errorBody)

	case strings.EqualFold("InvalidGrantTokenException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantTokenException(response, errorBody)

	case strings.EqualFold("InvalidKeyUsageException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidKeyUsageException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("KeyUnavailableException", errorCode):
		return awsAwsjson11_deserializeErrorKeyUnavailableException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpGenerateDataKey struct {
}

func (*awsAwsjson11_deserializeOpGenerateDataKey) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpGenerateDataKey) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorGenerateDataKey(response, &metadata)
	}
	output := &GenerateDataKeyOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentGenerateDataKeyOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorGenerateDataKey(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("DryRunOperationException", errorCode):
		return awsAwsjson11_deserializeErrorDryRunOperationException(response, errorBody)

	case strings.EqualFold("InvalidGrantTokenException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantTokenException(response, errorBody)

	case strings.EqualFold("InvalidKeyUsageException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidKeyUsageException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("KeyUnavailableException", errorCode):
		return awsAwsjson11_deserializeErrorKeyUnavailableException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpGenerateDataKeyPair struct {
}

func (*awsAwsjson11_deserializeOpGenerateDataKeyPair) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpGenerateDataKeyPair) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorGenerateDataKeyPair(response, &metadata)
	}
	output := &GenerateDataKeyPairOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentGenerateDataKeyPairOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorGenerateDataKeyPair(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("DryRunOperationException", errorCode):
		return awsAwsjson11_deserializeErrorDryRunOperationException(response, errorBody)

	case strings.EqualFold("InvalidGrantTokenException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantTokenException(response, errorBody)

	case strings.EqualFold("InvalidKeyUsageException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidKeyUsageException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("KeyUnavailableException", errorCode):
		return awsAwsjson11_deserializeErrorKeyUnavailableException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	case strings.EqualFold("UnsupportedOperationException", errorCode):
		return awsAwsjson11_deserializeErrorUnsupportedOperationException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpGenerateDataKeyPairWithoutPlaintext struct {
}

func (*awsAwsjson11_deserializeOpGenerateDataKeyPairWithoutPlaintext) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpGenerateDataKeyPairWithoutPlaintext) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorGenerateDataKeyPairWithoutPlaintext(response, &metadata)
	}
	output := &GenerateDataKeyPairWithoutPlaintextOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentGenerateDataKeyPairWithoutPlaintextOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorGenerateDataKeyPairWithoutPlaintext(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("DryRunOperationException", errorCode):
		return awsAwsjson11_deserializeErrorDryRunOperationException(response, errorBody)

	case strings.EqualFold("InvalidGrantTokenException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantTokenException(response, errorBody)

	case strings.EqualFold("InvalidKeyUsageException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidKeyUsageException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("KeyUnavailableException", errorCode):
		return awsAwsjson11_deserializeErrorKeyUnavailableException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	case strings.EqualFold("UnsupportedOperationException", errorCode):
		return awsAwsjson11_deserializeErrorUnsupportedOperationException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpGenerateDataKeyWithoutPlaintext struct {
}

func (*awsAwsjson11_deserializeOpGenerateDataKeyWithoutPlaintext) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpGenerateDataKeyWithoutPlaintext) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorGenerateDataKeyWithoutPlaintext(response, &metadata)
	}
	output := &GenerateDataKeyWithoutPlaintextOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentGenerateDataKeyWithoutPlaintextOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorGenerateDataKeyWithoutPlaintext(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("DryRunOperationException", errorCode):
		return awsAwsjson11_deserializeErrorDryRunOperationException(response, errorBody)

	case strings.EqualFold("InvalidGrantTokenException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantTokenException(response, errorBody)

	case strings.EqualFold("InvalidKeyUsageException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidKeyUsageException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("KeyUnavailableException", errorCode):
		return awsAwsjson11_deserializeErrorKeyUnavailableException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpGenerateMac struct {
}

func (*awsAwsjson11_deserializeOpGenerateMac) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpGenerateMac) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorGenerateMac(response, &metadata)
	}
	output := &GenerateMacOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentGenerateMacOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorGenerateMac(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("DryRunOperationException", errorCode):
		return awsAwsjson11_deserializeErrorDryRunOperationException(response, errorBody)

	case strings.EqualFold("InvalidGrantTokenException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantTokenException(response, errorBody)

	case strings.EqualFold("InvalidKeyUsageException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidKeyUsageException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("KeyUnavailableException", errorCode):
		return awsAwsjson11_deserializeErrorKeyUnavailableException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpGenerateRandom struct {
}

func (*awsAwsjson11_deserializeOpGenerateRandom) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpGenerateRandom) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorGenerateRandom(response, &metadata)
	}
	output := &GenerateRandomOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentGenerateRandomOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorGenerateRandom(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("CustomKeyStoreInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorCustomKeyStoreInvalidStateException(response, errorBody)

	case strings.EqualFold("CustomKeyStoreNotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorCustomKeyStoreNotFoundException(response, errorBody)

	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("UnsupportedOperationException", errorCode):
		return awsAwsjson11_deserializeErrorUnsupportedOperationException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpGetKeyPolicy struct {
}

func (*awsAwsjson11_deserializeOpGetKeyPolicy) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpGetKeyPolicy) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorGetKeyPolicy(response, &metadata)
	}
	output := &GetKeyPolicyOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentGetKeyPolicyOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorGetKeyPolicy(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpGetKeyRotationStatus struct {
}

func (*awsAwsjson11_deserializeOpGetKeyRotationStatus) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpGetKeyRotationStatus) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorGetKeyRotationStatus(response, &metadata)
	}
	output := &GetKeyRotationStatusOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentGetKeyRotationStatusOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorGetKeyRotationStatus(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	case strings.EqualFold("UnsupportedOperationException", errorCode):
		return awsAwsjson11_deserializeErrorUnsupportedOperationException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpGetParametersForImport struct {
}

func (*awsAwsjson11_deserializeOpGetParametersForImport) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpGetParametersForImport) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorGetParametersForImport(response, &metadata)
	}
	output := &GetParametersForImportOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentGetParametersForImportOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorGetParametersForImport(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	case strings.EqualFold("UnsupportedOperationException", errorCode):
		return awsAwsjson11_deserializeErrorUnsupportedOperationException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpGetPublicKey struct {
}

func (*awsAwsjson11_deserializeOpGetPublicKey) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpGetPublicKey) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorGetPublicKey(response, &metadata)
	}
	output := &GetPublicKeyOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentGetPublicKeyOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorGetPublicKey(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("InvalidGrantTokenException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantTokenException(response, errorBody)

	case strings.EqualFold("InvalidKeyUsageException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidKeyUsageException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("KeyUnavailableException", errorCode):
		return awsAwsjson11_deserializeErrorKeyUnavailableException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	case strings.EqualFold("UnsupportedOperationException", errorCode):
		return awsAwsjson11_deserializeErrorUnsupportedOperationException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpImportKeyMaterial struct {
}

func (*awsAwsjson11_deserializeOpImportKeyMaterial) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpImportKeyMaterial) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorImportKeyMaterial(response, &metadata)
	}
	output := &ImportKeyMaterialOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentImportKeyMaterialOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorImportKeyMaterial(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("ExpiredImportTokenException", errorCode):
		return awsAwsjson11_deserializeErrorExpiredImportTokenException(response, errorBody)

	case strings.EqualFold("IncorrectKeyMaterialException", errorCode):
		return awsAwsjson11_deserializeErrorIncorrectKeyMaterialException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("InvalidCiphertextException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidCiphertextException(response, errorBody)

	case strings.EqualFold("InvalidImportTokenException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidImportTokenException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	case strings.EqualFold("UnsupportedOperationException", errorCode):
		return awsAwsjson11_deserializeErrorUnsupportedOperationException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpListAliases struct {
}

func (*awsAwsjson11_deserializeOpListAliases) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpListAliases) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorListAliases(response, &metadata)
	}
	output := &ListAliasesOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentListAliasesOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorListAliases(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("InvalidMarkerException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidMarkerException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpListGrants struct {
}

func (*awsAwsjson11_deserializeOpListGrants) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpListGrants) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorListGrants(response, &metadata)
	}
	output := &ListGrantsOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentListGrantsOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorListGrants(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("InvalidGrantIdException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantIdException(response, errorBody)

	case strings.EqualFold("InvalidMarkerException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidMarkerException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpListKeyPolicies struct {
}

func (*awsAwsjson11_deserializeOpListKeyPolicies) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpListKeyPolicies) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorListKeyPolicies(response, &metadata)
	}
	output := &ListKeyPoliciesOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentListKeyPoliciesOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorListKeyPolicies(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpListKeyRotations struct {
}

func (*awsAwsjson11_deserializeOpListKeyRotations) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpListKeyRotations) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorListKeyRotations(response, &metadata)
	}
	output := &ListKeyRotationsOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentListKeyRotationsOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorListKeyRotations(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("InvalidMarkerException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidMarkerException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	case strings.EqualFold("UnsupportedOperationException", errorCode):
		return awsAwsjson11_deserializeErrorUnsupportedOperationException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpListKeys struct {
}

func (*awsAwsjson11_deserializeOpListKeys) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpListKeys) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorListKeys(response, &metadata)
	}
	output := &ListKeysOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentListKeysOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorListKeys(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidMarkerException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidMarkerException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpListResourceTags struct {
}

func (*awsAwsjson11_deserializeOpListResourceTags) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpListResourceTags) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorListResourceTags(response, &metadata)
	}
	output := &ListResourceTagsOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentListResourceTagsOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorListResourceTags(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("InvalidMarkerException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidMarkerException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpListRetirableGrants struct {
}

func (*awsAwsjson11_deserializeOpListRetirableGrants) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpListRetirableGrants) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorListRetirableGrants(response, &metadata)
	}
	output := &ListRetirableGrantsOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentListRetirableGrantsOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorListRetirableGrants(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("InvalidMarkerException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidMarkerException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpPutKeyPolicy struct {
}

func (*awsAwsjson11_deserializeOpPutKeyPolicy) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpPutKeyPolicy) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorPutKeyPolicy(response, &metadata)
	}
	output := &PutKeyPolicyOutput{}
	out.Result = output

	if _, err = io.Copy(ioutil.Discard, response.Body); err != nil {
		return out, metadata, &smithy.DeserializationError{
			Err: fmt.Errorf("failed to discard response body, %w", err),
		}
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorPutKeyPolicy(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("LimitExceededException", errorCode):
		return awsAwsjson11_deserializeErrorLimitExceededException(response, errorBody)

	case strings.EqualFold("MalformedPolicyDocumentException", errorCode):
		return awsAwsjson11_deserializeErrorMalformedPolicyDocumentException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	case strings.EqualFold("UnsupportedOperationException", errorCode):
		return awsAwsjson11_deserializeErrorUnsupportedOperationException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpReEncrypt struct {
}

func (*awsAwsjson11_deserializeOpReEncrypt) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpReEncrypt) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorReEncrypt(response, &metadata)
	}
	output := &ReEncryptOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentReEncryptOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorReEncrypt(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("DryRunOperationException", errorCode):
		return awsAwsjson11_deserializeErrorDryRunOperationException(response, errorBody)

	case strings.EqualFold("IncorrectKeyException", errorCode):
		return awsAwsjson11_deserializeErrorIncorrectKeyException(response, errorBody)

	case strings.EqualFold("InvalidCiphertextException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidCiphertextException(response, errorBody)

	case strings.EqualFold("InvalidGrantTokenException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantTokenException(response, errorBody)

	case strings.EqualFold("InvalidKeyUsageException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidKeyUsageException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("KeyUnavailableException", errorCode):
		return awsAwsjson11_deserializeErrorKeyUnavailableException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpReplicateKey struct {
}

func (*awsAwsjson11_deserializeOpReplicateKey) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpReplicateKey) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorReplicateKey(response, &metadata)
	}
	output := &ReplicateKeyOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentReplicateKeyOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorReplicateKey(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("AlreadyExistsException", errorCode):
		return awsAwsjson11_deserializeErrorAlreadyExistsException(response, errorBody)

	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("LimitExceededException", errorCode):
		return awsAwsjson11_deserializeErrorLimitExceededException(response, errorBody)

	case strings.EqualFold("MalformedPolicyDocumentException", errorCode):
		return awsAwsjson11_deserializeErrorMalformedPolicyDocumentException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	case strings.EqualFold("TagException", errorCode):
		return awsAwsjson11_deserializeErrorTagException(response, errorBody)

	case strings.EqualFold("UnsupportedOperationException", errorCode):
		return awsAwsjson11_deserializeErrorUnsupportedOperationException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpRetireGrant struct {
}

func (*awsAwsjson11_deserializeOpRetireGrant) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpRetireGrant) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorRetireGrant(response, &metadata)
	}
	output := &RetireGrantOutput{}
	out.Result = output

	if _, err = io.Copy(ioutil.Discard, response.Body); err != nil {
		return out, metadata, &smithy.DeserializationError{
			Err: fmt.Errorf("failed to discard response body, %w", err),
		}
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorRetireGrant(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("DryRunOperationException", errorCode):
		return awsAwsjson11_deserializeErrorDryRunOperationException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("InvalidGrantIdException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantIdException(response, errorBody)

	case strings.EqualFold("InvalidGrantTokenException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantTokenException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpRevokeGrant struct {
}

func (*awsAwsjson11_deserializeOpRevokeGrant) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpRevokeGrant) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorRevokeGrant(response, &metadata)
	}
	output := &RevokeGrantOutput{}
	out.Result = output

	if _, err = io.Copy(ioutil.Discard, response.Body); err != nil {
		return out, metadata, &smithy.DeserializationError{
			Err: fmt.Errorf("failed to discard response body, %w", err),
		}
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorRevokeGrant(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("DryRunOperationException", errorCode):
		return awsAwsjson11_deserializeErrorDryRunOperationException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("InvalidGrantIdException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantIdException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpRotateKeyOnDemand struct {
}

func (*awsAwsjson11_deserializeOpRotateKeyOnDemand) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpRotateKeyOnDemand) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorRotateKeyOnDemand(response, &metadata)
	}
	output := &RotateKeyOnDemandOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentRotateKeyOnDemandOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorRotateKeyOnDemand(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("ConflictException", errorCode):
		return awsAwsjson11_deserializeErrorConflictException(response, errorBody)

	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("LimitExceededException", errorCode):
		return awsAwsjson11_deserializeErrorLimitExceededException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	case strings.EqualFold("UnsupportedOperationException", errorCode):
		return awsAwsjson11_deserializeErrorUnsupportedOperationException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpScheduleKeyDeletion struct {
}

func (*awsAwsjson11_deserializeOpScheduleKeyDeletion) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpScheduleKeyDeletion) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorScheduleKeyDeletion(response, &metadata)
	}
	output := &ScheduleKeyDeletionOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentScheduleKeyDeletionOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorScheduleKeyDeletion(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpSign struct {
}

func (*awsAwsjson11_deserializeOpSign) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpSign) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorSign(response, &metadata)
	}
	output := &SignOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentSignOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorSign(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("DryRunOperationException", errorCode):
		return awsAwsjson11_deserializeErrorDryRunOperationException(response, errorBody)

	case strings.EqualFold("InvalidGrantTokenException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantTokenException(response, errorBody)

	case strings.EqualFold("InvalidKeyUsageException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidKeyUsageException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("KeyUnavailableException", errorCode):
		return awsAwsjson11_deserializeErrorKeyUnavailableException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpTagResource struct {
}

func (*awsAwsjson11_deserializeOpTagResource) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpTagResource) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorTagResource(response, &metadata)
	}
	output := &TagResourceOutput{}
	out.Result = output

	if _, err = io.Copy(ioutil.Discard, response.Body); err != nil {
		return out, metadata, &smithy.DeserializationError{
			Err: fmt.Errorf("failed to discard response body, %w", err),
		}
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorTagResource(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("LimitExceededException", errorCode):
		return awsAwsjson11_deserializeErrorLimitExceededException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	case strings.EqualFold("TagException", errorCode):
		return awsAwsjson11_deserializeErrorTagException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpUntagResource struct {
}

func (*awsAwsjson11_deserializeOpUntagResource) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpUntagResource) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorUntagResource(response, &metadata)
	}
	output := &UntagResourceOutput{}
	out.Result = output

	if _, err = io.Copy(ioutil.Discard, response.Body); err != nil {
		return out, metadata, &smithy.DeserializationError{
			Err: fmt.Errorf("failed to discard response body, %w", err),
		}
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorUntagResource(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	case strings.EqualFold("TagException", errorCode):
		return awsAwsjson11_deserializeErrorTagException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpUpdateAlias struct {
}

func (*awsAwsjson11_deserializeOpUpdateAlias) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpUpdateAlias) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorUpdateAlias(response, &metadata)
	}
	output := &UpdateAliasOutput{}
	out.Result = output

	if _, err = io.Copy(ioutil.Discard, response.Body); err != nil {
		return out, metadata, &smithy.DeserializationError{
			Err: fmt.Errorf("failed to discard response body, %w", err),
		}
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorUpdateAlias(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("LimitExceededException", errorCode):
		return awsAwsjson11_deserializeErrorLimitExceededException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpUpdateCustomKeyStore struct {
}

func (*awsAwsjson11_deserializeOpUpdateCustomKeyStore) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpUpdateCustomKeyStore) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorUpdateCustomKeyStore(response, &metadata)
	}
	output := &UpdateCustomKeyStoreOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentUpdateCustomKeyStoreOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorUpdateCustomKeyStore(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("CloudHsmClusterInvalidConfigurationException", errorCode):
		return awsAwsjson11_deserializeErrorCloudHsmClusterInvalidConfigurationException(response, errorBody)

	case strings.EqualFold("CloudHsmClusterNotActiveException", errorCode):
		return awsAwsjson11_deserializeErrorCloudHsmClusterNotActiveException(response, errorBody)

	case strings.EqualFold("CloudHsmClusterNotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorCloudHsmClusterNotFoundException(response, errorBody)

	case strings.EqualFold("CloudHsmClusterNotRelatedException", errorCode):
		return awsAwsjson11_deserializeErrorCloudHsmClusterNotRelatedException(response, errorBody)

	case strings.EqualFold("CustomKeyStoreInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorCustomKeyStoreInvalidStateException(response, errorBody)

	case strings.EqualFold("CustomKeyStoreNameInUseException", errorCode):
		return awsAwsjson11_deserializeErrorCustomKeyStoreNameInUseException(response, errorBody)

	case strings.EqualFold("CustomKeyStoreNotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorCustomKeyStoreNotFoundException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("XksProxyIncorrectAuthenticationCredentialException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyIncorrectAuthenticationCredentialException(response, errorBody)

	case strings.EqualFold("XksProxyInvalidConfigurationException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyInvalidConfigurationException(response, errorBody)

	case strings.EqualFold("XksProxyInvalidResponseException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyInvalidResponseException(response, errorBody)

	case strings.EqualFold("XksProxyUriEndpointInUseException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyUriEndpointInUseException(response, errorBody)

	case strings.EqualFold("XksProxyUriInUseException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyUriInUseException(response, errorBody)

	case strings.EqualFold("XksProxyUriUnreachableException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyUriUnreachableException(response, errorBody)

	case strings.EqualFold("XksProxyVpcEndpointServiceInUseException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyVpcEndpointServiceInUseException(response, errorBody)

	case strings.EqualFold("XksProxyVpcEndpointServiceInvalidConfigurationException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyVpcEndpointServiceInvalidConfigurationException(response, errorBody)

	case strings.EqualFold("XksProxyVpcEndpointServiceNotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorXksProxyVpcEndpointServiceNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpUpdateKeyDescription struct {
}

func (*awsAwsjson11_deserializeOpUpdateKeyDescription) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpUpdateKeyDescription) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorUpdateKeyDescription(response, &metadata)
	}
	output := &UpdateKeyDescriptionOutput{}
	out.Result = output

	if _, err = io.Copy(ioutil.Discard, response.Body); err != nil {
		return out, metadata, &smithy.DeserializationError{
			Err: fmt.Errorf("failed to discard response body, %w", err),
		}
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorUpdateKeyDescription(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpUpdatePrimaryRegion struct {
}

func (*awsAwsjson11_deserializeOpUpdatePrimaryRegion) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpUpdatePrimaryRegion) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorUpdatePrimaryRegion(response, &metadata)
	}
	output := &UpdatePrimaryRegionOutput{}
	out.Result = output

	if _, err = io.Copy(ioutil.Discard, response.Body); err != nil {
		return out, metadata, &smithy.DeserializationError{
			Err: fmt.Errorf("failed to discard response body, %w", err),
		}
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorUpdatePrimaryRegion(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("InvalidArnException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidArnException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	case strings.EqualFold("UnsupportedOperationException", errorCode):
		return awsAwsjson11_deserializeErrorUnsupportedOperationException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpVerify struct {
}

func (*awsAwsjson11_deserializeOpVerify) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpVerify) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorVerify(response, &metadata)
	}
	output := &VerifyOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentVerifyOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorVerify(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DependencyTimeoutException", errorCode):
		return awsAwsjson11_deserializeErrorDependencyTimeoutException(response, errorBody)

	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("DryRunOperationException", errorCode):
		return awsAwsjson11_deserializeErrorDryRunOperationException(response, errorBody)

	case strings.EqualFold("InvalidGrantTokenException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantTokenException(response, errorBody)

	case strings.EqualFold("InvalidKeyUsageException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidKeyUsageException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidSignatureException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidSignatureException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("KeyUnavailableException", errorCode):
		return awsAwsjson11_deserializeErrorKeyUnavailableException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsjson11_deserializeOpVerifyMac struct {
}

func (*awsAwsjson11_deserializeOpVerifyMac) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsjson11_deserializeOpVerifyMac) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	_, span := tracing.StartSpan(ctx, "OperationDeserializer")
	endTimer := startMetricTimer(ctx, "client.call.deserialization_duration")
	defer endTimer()
	defer span.End()
	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsjson11_deserializeOpErrorVerifyMac(response, &metadata)
	}
	output := &VerifyMacOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(response.Body, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	err = awsAwsjson11_deserializeOpDocumentVerifyMacOutput(&output, shape)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsjson11_deserializeOpErrorVerifyMac(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	headerCode := response.Header.Get("X-Amzn-ErrorType")

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	bodyInfo, err := getProtocolErrorInfo(decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	if typ, ok := resolveProtocolErrorType(headerCode, bodyInfo); ok {
		errorCode = restjson.SanitizeErrorCode(typ)
	}
	if len(bodyInfo.Message) != 0 {
		errorMessage = bodyInfo.Message
	}
	switch {
	case strings.EqualFold("DisabledException", errorCode):
		return awsAwsjson11_deserializeErrorDisabledException(response, errorBody)

	case strings.EqualFold("DryRunOperationException", errorCode):
		return awsAwsjson11_deserializeErrorDryRunOperationException(response, errorBody)

	case strings.EqualFold("InvalidGrantTokenException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidGrantTokenException(response, errorBody)

	case strings.EqualFold("InvalidKeyUsageException", errorCode):
		return awsAwsjson11_deserializeErrorInvalidKeyUsageException(response, errorBody)

	case strings.EqualFold("KMSInternalException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInternalException(response, errorBody)

	case strings.EqualFold("KMSInvalidMacException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidMacException(response, errorBody)

	case strings.EqualFold("KMSInvalidStateException", errorCode):
		return awsAwsjson11_deserializeErrorKMSInvalidStateException(response, errorBody)

	case strings.EqualFold("KeyUnavailableException", errorCode):
		return awsAwsjson11_deserializeErrorKeyUnavailableException(response, errorBody)

	case strings.EqualFold("NotFoundException", errorCode):
		return awsAwsjson11_deserializeErrorNotFoundException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

func awsAwsjson11_deserializeErrorAlreadyExistsException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.AlreadyExistsException{}
	err := awsAwsjson11_deserializeDocumentAlreadyExistsException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorCloudHsmClusterInUseException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.CloudHsmClusterInUseException{}
	err := awsAwsjson11_deserializeDocumentCloudHsmClusterInUseException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorCloudHsmClusterInvalidConfigurationException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.CloudHsmClusterInvalidConfigurationException{}
	err := awsAwsjson11_deserializeDocumentCloudHsmClusterInvalidConfigurationException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorCloudHsmClusterNotActiveException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.CloudHsmClusterNotActiveException{}
	err := awsAwsjson11_deserializeDocumentCloudHsmClusterNotActiveException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorCloudHsmClusterNotFoundException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.CloudHsmClusterNotFoundException{}
	err := awsAwsjson11_deserializeDocumentCloudHsmClusterNotFoundException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorCloudHsmClusterNotRelatedException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.CloudHsmClusterNotRelatedException{}
	err := awsAwsjson11_deserializeDocumentCloudHsmClusterNotRelatedException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorConflictException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.ConflictException{}
	err := awsAwsjson11_deserializeDocumentConflictException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorCustomKeyStoreHasCMKsException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.CustomKeyStoreHasCMKsException{}
	err := awsAwsjson11_deserializeDocumentCustomKeyStoreHasCMKsException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorCustomKeyStoreInvalidStateException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.CustomKeyStoreInvalidStateException{}
	err := awsAwsjson11_deserializeDocumentCustomKeyStoreInvalidStateException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorCustomKeyStoreNameInUseException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.CustomKeyStoreNameInUseException{}
	err := awsAwsjson11_deserializeDocumentCustomKeyStoreNameInUseException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorCustomKeyStoreNotFoundException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.CustomKeyStoreNotFoundException{}
	err := awsAwsjson11_deserializeDocumentCustomKeyStoreNotFoundException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorDependencyTimeoutException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.DependencyTimeoutException{}
	err := awsAwsjson11_deserializeDocumentDependencyTimeoutException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorDisabledException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.DisabledException{}
	err := awsAwsjson11_deserializeDocumentDisabledException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorDryRunOperationException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.DryRunOperationException{}
	err := awsAwsjson11_deserializeDocumentDryRunOperationException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorExpiredImportTokenException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.ExpiredImportTokenException{}
	err := awsAwsjson11_deserializeDocumentExpiredImportTokenException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorIncorrectKeyException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.IncorrectKeyException{}
	err := awsAwsjson11_deserializeDocumentIncorrectKeyException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorIncorrectKeyMaterialException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.IncorrectKeyMaterialException{}
	err := awsAwsjson11_deserializeDocumentIncorrectKeyMaterialException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorIncorrectTrustAnchorException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.IncorrectTrustAnchorException{}
	err := awsAwsjson11_deserializeDocumentIncorrectTrustAnchorException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorInvalidAliasNameException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.InvalidAliasNameException{}
	err := awsAwsjson11_deserializeDocumentInvalidAliasNameException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorInvalidArnException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.InvalidArnException{}
	err := awsAwsjson11_deserializeDocumentInvalidArnException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorInvalidCiphertextException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.InvalidCiphertextException{}
	err := awsAwsjson11_deserializeDocumentInvalidCiphertextException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorInvalidGrantIdException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.InvalidGrantIdException{}
	err := awsAwsjson11_deserializeDocumentInvalidGrantIdException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorInvalidGrantTokenException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.InvalidGrantTokenException{}
	err := awsAwsjson11_deserializeDocumentInvalidGrantTokenException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorInvalidImportTokenException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.InvalidImportTokenException{}
	err := awsAwsjson11_deserializeDocumentInvalidImportTokenException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorInvalidKeyUsageException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.InvalidKeyUsageException{}
	err := awsAwsjson11_deserializeDocumentInvalidKeyUsageException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorInvalidMarkerException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.InvalidMarkerException{}
	err := awsAwsjson11_deserializeDocumentInvalidMarkerException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorKeyUnavailableException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.KeyUnavailableException{}
	err := awsAwsjson11_deserializeDocumentKeyUnavailableException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorKMSInternalException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.KMSInternalException{}
	err := awsAwsjson11_deserializeDocumentKMSInternalException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorKMSInvalidMacException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.KMSInvalidMacException{}
	err := awsAwsjson11_deserializeDocumentKMSInvalidMacException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorKMSInvalidSignatureException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.KMSInvalidSignatureException{}
	err := awsAwsjson11_deserializeDocumentKMSInvalidSignatureException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorKMSInvalidStateException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.KMSInvalidStateException{}
	err := awsAwsjson11_deserializeDocumentKMSInvalidStateException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorLimitExceededException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.LimitExceededException{}
	err := awsAwsjson11_deserializeDocumentLimitExceededException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorMalformedPolicyDocumentException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.MalformedPolicyDocumentException{}
	err := awsAwsjson11_deserializeDocumentMalformedPolicyDocumentException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorNotFoundException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.NotFoundException{}
	err := awsAwsjson11_deserializeDocumentNotFoundException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorTagException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.TagException{}
	err := awsAwsjson11_deserializeDocumentTagException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorUnsupportedOperationException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.UnsupportedOperationException{}
	err := awsAwsjson11_deserializeDocumentUnsupportedOperationException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorXksKeyAlreadyInUseException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.XksKeyAlreadyInUseException{}
	err := awsAwsjson11_deserializeDocumentXksKeyAlreadyInUseException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorXksKeyInvalidConfigurationException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.XksKeyInvalidConfigurationException{}
	err := awsAwsjson11_deserializeDocumentXksKeyInvalidConfigurationException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorXksKeyNotFoundException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.XksKeyNotFoundException{}
	err := awsAwsjson11_deserializeDocumentXksKeyNotFoundException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorXksProxyIncorrectAuthenticationCredentialException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.XksProxyIncorrectAuthenticationCredentialException{}
	err := awsAwsjson11_deserializeDocumentXksProxyIncorrectAuthenticationCredentialException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorXksProxyInvalidConfigurationException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.XksProxyInvalidConfigurationException{}
	err := awsAwsjson11_deserializeDocumentXksProxyInvalidConfigurationException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorXksProxyInvalidResponseException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.XksProxyInvalidResponseException{}
	err := awsAwsjson11_deserializeDocumentXksProxyInvalidResponseException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorXksProxyUriEndpointInUseException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.XksProxyUriEndpointInUseException{}
	err := awsAwsjson11_deserializeDocumentXksProxyUriEndpointInUseException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorXksProxyUriInUseException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.XksProxyUriInUseException{}
	err := awsAwsjson11_deserializeDocumentXksProxyUriInUseException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorXksProxyUriUnreachableException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.XksProxyUriUnreachableException{}
	err := awsAwsjson11_deserializeDocumentXksProxyUriUnreachableException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorXksProxyVpcEndpointServiceInUseException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.XksProxyVpcEndpointServiceInUseException{}
	err := awsAwsjson11_deserializeDocumentXksProxyVpcEndpointServiceInUseException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorXksProxyVpcEndpointServiceInvalidConfigurationException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.XksProxyVpcEndpointServiceInvalidConfigurationException{}
	err := awsAwsjson11_deserializeDocumentXksProxyVpcEndpointServiceInvalidConfigurationException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeErrorXksProxyVpcEndpointServiceNotFoundException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])

	body := io.TeeReader(errorBody, ringBuffer)
	decoder := json.NewDecoder(body)
	decoder.UseNumber()
	var shape interface{}
	if err := decoder.Decode(&shape); err != nil && err != io.EOF {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	output := &types.XksProxyVpcEndpointServiceNotFoundException{}
	err := awsAwsjson11_deserializeDocumentXksProxyVpcEndpointServiceNotFoundException(&output, shape)

	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return err
	}

	errorBody.Seek(0, io.SeekStart)
	return output
}

func awsAwsjson11_deserializeDocumentAliasList(v *[]types.AliasListEntry, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.([]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var cv []types.AliasListEntry
	if *v == nil {
		cv = []types.AliasListEntry{}
	} else {
		cv = *v
	}

	for _, value := range shape {
		var col types.AliasListEntry
		destAddr := &col
		if err := awsAwsjson11_deserializeDocumentAliasListEntry(&destAddr, value); err != nil {
			return err
		}
		col = *destAddr
		cv = append(cv, col)

	}
	*v = cv
	return nil
}

func awsAwsjson11_deserializeDocumentAliasListEntry(v **types.AliasListEntry, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.AliasListEntry
	if *v == nil {
		sv = &types.AliasListEntry{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "AliasArn":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ArnType to be of type string, got %T instead", value)
				}
				sv.AliasArn = ptr.String(jtv)
			}

		case "AliasName":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected AliasNameType to be of type string, got %T instead", value)
				}
				sv.AliasName = ptr.String(jtv)
			}

		case "CreationDate":
			if value != nil {
				switch jtv := value.(type) {
				case json.Number:
					f64, err := jtv.Float64()
					if err != nil {
						return err
					}
					sv.CreationDate = ptr.Time(smithytime.ParseEpochSeconds(f64))

				default:
					return fmt.Errorf("expected DateType to be a JSON Number, got %T instead", value)

				}
			}

		case "LastUpdatedDate":
			if value != nil {
				switch jtv := value.(type) {
				case json.Number:
					f64, err := jtv.Float64()
					if err != nil {
						return err
					}
					sv.LastUpdatedDate = ptr.Time(smithytime.ParseEpochSeconds(f64))

				default:
					return fmt.Errorf("expected DateType to be a JSON Number, got %T instead", value)

				}
			}

		case "TargetKeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.TargetKeyId = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentAlreadyExistsException(v **types.AlreadyExistsException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.AlreadyExistsException
	if *v == nil {
		sv = &types.AlreadyExistsException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentCloudHsmClusterInUseException(v **types.CloudHsmClusterInUseException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.CloudHsmClusterInUseException
	if *v == nil {
		sv = &types.CloudHsmClusterInUseException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentCloudHsmClusterInvalidConfigurationException(v **types.CloudHsmClusterInvalidConfigurationException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.CloudHsmClusterInvalidConfigurationException
	if *v == nil {
		sv = &types.CloudHsmClusterInvalidConfigurationException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentCloudHsmClusterNotActiveException(v **types.CloudHsmClusterNotActiveException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.CloudHsmClusterNotActiveException
	if *v == nil {
		sv = &types.CloudHsmClusterNotActiveException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentCloudHsmClusterNotFoundException(v **types.CloudHsmClusterNotFoundException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.CloudHsmClusterNotFoundException
	if *v == nil {
		sv = &types.CloudHsmClusterNotFoundException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentCloudHsmClusterNotRelatedException(v **types.CloudHsmClusterNotRelatedException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.CloudHsmClusterNotRelatedException
	if *v == nil {
		sv = &types.CloudHsmClusterNotRelatedException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentConflictException(v **types.ConflictException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.ConflictException
	if *v == nil {
		sv = &types.ConflictException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentCustomKeyStoreHasCMKsException(v **types.CustomKeyStoreHasCMKsException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.CustomKeyStoreHasCMKsException
	if *v == nil {
		sv = &types.CustomKeyStoreHasCMKsException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentCustomKeyStoreInvalidStateException(v **types.CustomKeyStoreInvalidStateException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.CustomKeyStoreInvalidStateException
	if *v == nil {
		sv = &types.CustomKeyStoreInvalidStateException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentCustomKeyStoreNameInUseException(v **types.CustomKeyStoreNameInUseException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.CustomKeyStoreNameInUseException
	if *v == nil {
		sv = &types.CustomKeyStoreNameInUseException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentCustomKeyStoreNotFoundException(v **types.CustomKeyStoreNotFoundException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.CustomKeyStoreNotFoundException
	if *v == nil {
		sv = &types.CustomKeyStoreNotFoundException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentCustomKeyStoresList(v *[]types.CustomKeyStoresListEntry, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.([]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var cv []types.CustomKeyStoresListEntry
	if *v == nil {
		cv = []types.CustomKeyStoresListEntry{}
	} else {
		cv = *v
	}

	for _, value := range shape {
		var col types.CustomKeyStoresListEntry
		destAddr := &col
		if err := awsAwsjson11_deserializeDocumentCustomKeyStoresListEntry(&destAddr, value); err != nil {
			return err
		}
		col = *destAddr
		cv = append(cv, col)

	}
	*v = cv
	return nil
}

func awsAwsjson11_deserializeDocumentCustomKeyStoresListEntry(v **types.CustomKeyStoresListEntry, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.CustomKeyStoresListEntry
	if *v == nil {
		sv = &types.CustomKeyStoresListEntry{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "CloudHsmClusterId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CloudHsmClusterIdType to be of type string, got %T instead", value)
				}
				sv.CloudHsmClusterId = ptr.String(jtv)
			}

		case "ConnectionErrorCode":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ConnectionErrorCodeType to be of type string, got %T instead", value)
				}
				sv.ConnectionErrorCode = types.ConnectionErrorCodeType(jtv)
			}

		case "ConnectionState":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ConnectionStateType to be of type string, got %T instead", value)
				}
				sv.ConnectionState = types.ConnectionStateType(jtv)
			}

		case "CreationDate":
			if value != nil {
				switch jtv := value.(type) {
				case json.Number:
					f64, err := jtv.Float64()
					if err != nil {
						return err
					}
					sv.CreationDate = ptr.Time(smithytime.ParseEpochSeconds(f64))

				default:
					return fmt.Errorf("expected DateType to be a JSON Number, got %T instead", value)

				}
			}

		case "CustomKeyStoreId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CustomKeyStoreIdType to be of type string, got %T instead", value)
				}
				sv.CustomKeyStoreId = ptr.String(jtv)
			}

		case "CustomKeyStoreName":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CustomKeyStoreNameType to be of type string, got %T instead", value)
				}
				sv.CustomKeyStoreName = ptr.String(jtv)
			}

		case "CustomKeyStoreType":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CustomKeyStoreType to be of type string, got %T instead", value)
				}
				sv.CustomKeyStoreType = types.CustomKeyStoreType(jtv)
			}

		case "TrustAnchorCertificate":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected TrustAnchorCertificateType to be of type string, got %T instead", value)
				}
				sv.TrustAnchorCertificate = ptr.String(jtv)
			}

		case "XksProxyConfiguration":
			if err := awsAwsjson11_deserializeDocumentXksProxyConfigurationType(&sv.XksProxyConfiguration, value); err != nil {
				return err
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentDependencyTimeoutException(v **types.DependencyTimeoutException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.DependencyTimeoutException
	if *v == nil {
		sv = &types.DependencyTimeoutException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentDisabledException(v **types.DisabledException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.DisabledException
	if *v == nil {
		sv = &types.DisabledException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentDryRunOperationException(v **types.DryRunOperationException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.DryRunOperationException
	if *v == nil {
		sv = &types.DryRunOperationException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentEncryptionAlgorithmSpecList(v *[]types.EncryptionAlgorithmSpec, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.([]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var cv []types.EncryptionAlgorithmSpec
	if *v == nil {
		cv = []types.EncryptionAlgorithmSpec{}
	} else {
		cv = *v
	}

	for _, value := range shape {
		var col types.EncryptionAlgorithmSpec
		if value != nil {
			jtv, ok := value.(string)
			if !ok {
				return fmt.Errorf("expected EncryptionAlgorithmSpec to be of type string, got %T instead", value)
			}
			col = types.EncryptionAlgorithmSpec(jtv)
		}
		cv = append(cv, col)

	}
	*v = cv
	return nil
}

func awsAwsjson11_deserializeDocumentEncryptionContextType(v *map[string]string, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var mv map[string]string
	if *v == nil {
		mv = map[string]string{}
	} else {
		mv = *v
	}

	for key, value := range shape {
		var parsedVal string
		if value != nil {
			jtv, ok := value.(string)
			if !ok {
				return fmt.Errorf("expected EncryptionContextValue to be of type string, got %T instead", value)
			}
			parsedVal = jtv
		}
		mv[key] = parsedVal

	}
	*v = mv
	return nil
}

func awsAwsjson11_deserializeDocumentExpiredImportTokenException(v **types.ExpiredImportTokenException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.ExpiredImportTokenException
	if *v == nil {
		sv = &types.ExpiredImportTokenException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentGrantConstraints(v **types.GrantConstraints, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.GrantConstraints
	if *v == nil {
		sv = &types.GrantConstraints{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "EncryptionContextEquals":
			if err := awsAwsjson11_deserializeDocumentEncryptionContextType(&sv.EncryptionContextEquals, value); err != nil {
				return err
			}

		case "EncryptionContextSubset":
			if err := awsAwsjson11_deserializeDocumentEncryptionContextType(&sv.EncryptionContextSubset, value); err != nil {
				return err
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentGrantList(v *[]types.GrantListEntry, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.([]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var cv []types.GrantListEntry
	if *v == nil {
		cv = []types.GrantListEntry{}
	} else {
		cv = *v
	}

	for _, value := range shape {
		var col types.GrantListEntry
		destAddr := &col
		if err := awsAwsjson11_deserializeDocumentGrantListEntry(&destAddr, value); err != nil {
			return err
		}
		col = *destAddr
		cv = append(cv, col)

	}
	*v = cv
	return nil
}

func awsAwsjson11_deserializeDocumentGrantListEntry(v **types.GrantListEntry, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.GrantListEntry
	if *v == nil {
		sv = &types.GrantListEntry{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "Constraints":
			if err := awsAwsjson11_deserializeDocumentGrantConstraints(&sv.Constraints, value); err != nil {
				return err
			}

		case "CreationDate":
			if value != nil {
				switch jtv := value.(type) {
				case json.Number:
					f64, err := jtv.Float64()
					if err != nil {
						return err
					}
					sv.CreationDate = ptr.Time(smithytime.ParseEpochSeconds(f64))

				default:
					return fmt.Errorf("expected DateType to be a JSON Number, got %T instead", value)

				}
			}

		case "GranteePrincipal":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected PrincipalIdType to be of type string, got %T instead", value)
				}
				sv.GranteePrincipal = ptr.String(jtv)
			}

		case "GrantId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected GrantIdType to be of type string, got %T instead", value)
				}
				sv.GrantId = ptr.String(jtv)
			}

		case "IssuingAccount":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected PrincipalIdType to be of type string, got %T instead", value)
				}
				sv.IssuingAccount = ptr.String(jtv)
			}

		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		case "Name":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected GrantNameType to be of type string, got %T instead", value)
				}
				sv.Name = ptr.String(jtv)
			}

		case "Operations":
			if err := awsAwsjson11_deserializeDocumentGrantOperationList(&sv.Operations, value); err != nil {
				return err
			}

		case "RetiringPrincipal":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected PrincipalIdType to be of type string, got %T instead", value)
				}
				sv.RetiringPrincipal = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentGrantOperationList(v *[]types.GrantOperation, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.([]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var cv []types.GrantOperation
	if *v == nil {
		cv = []types.GrantOperation{}
	} else {
		cv = *v
	}

	for _, value := range shape {
		var col types.GrantOperation
		if value != nil {
			jtv, ok := value.(string)
			if !ok {
				return fmt.Errorf("expected GrantOperation to be of type string, got %T instead", value)
			}
			col = types.GrantOperation(jtv)
		}
		cv = append(cv, col)

	}
	*v = cv
	return nil
}

func awsAwsjson11_deserializeDocumentIncorrectKeyException(v **types.IncorrectKeyException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.IncorrectKeyException
	if *v == nil {
		sv = &types.IncorrectKeyException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentIncorrectKeyMaterialException(v **types.IncorrectKeyMaterialException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.IncorrectKeyMaterialException
	if *v == nil {
		sv = &types.IncorrectKeyMaterialException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentIncorrectTrustAnchorException(v **types.IncorrectTrustAnchorException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.IncorrectTrustAnchorException
	if *v == nil {
		sv = &types.IncorrectTrustAnchorException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentInvalidAliasNameException(v **types.InvalidAliasNameException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.InvalidAliasNameException
	if *v == nil {
		sv = &types.InvalidAliasNameException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentInvalidArnException(v **types.InvalidArnException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.InvalidArnException
	if *v == nil {
		sv = &types.InvalidArnException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentInvalidCiphertextException(v **types.InvalidCiphertextException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.InvalidCiphertextException
	if *v == nil {
		sv = &types.InvalidCiphertextException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentInvalidGrantIdException(v **types.InvalidGrantIdException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.InvalidGrantIdException
	if *v == nil {
		sv = &types.InvalidGrantIdException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentInvalidGrantTokenException(v **types.InvalidGrantTokenException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.InvalidGrantTokenException
	if *v == nil {
		sv = &types.InvalidGrantTokenException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentInvalidImportTokenException(v **types.InvalidImportTokenException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.InvalidImportTokenException
	if *v == nil {
		sv = &types.InvalidImportTokenException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentInvalidKeyUsageException(v **types.InvalidKeyUsageException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.InvalidKeyUsageException
	if *v == nil {
		sv = &types.InvalidKeyUsageException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentInvalidMarkerException(v **types.InvalidMarkerException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.InvalidMarkerException
	if *v == nil {
		sv = &types.InvalidMarkerException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentKeyAgreementAlgorithmSpecList(v *[]types.KeyAgreementAlgorithmSpec, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.([]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var cv []types.KeyAgreementAlgorithmSpec
	if *v == nil {
		cv = []types.KeyAgreementAlgorithmSpec{}
	} else {
		cv = *v
	}

	for _, value := range shape {
		var col types.KeyAgreementAlgorithmSpec
		if value != nil {
			jtv, ok := value.(string)
			if !ok {
				return fmt.Errorf("expected KeyAgreementAlgorithmSpec to be of type string, got %T instead", value)
			}
			col = types.KeyAgreementAlgorithmSpec(jtv)
		}
		cv = append(cv, col)

	}
	*v = cv
	return nil
}

func awsAwsjson11_deserializeDocumentKeyList(v *[]types.KeyListEntry, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.([]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var cv []types.KeyListEntry
	if *v == nil {
		cv = []types.KeyListEntry{}
	} else {
		cv = *v
	}

	for _, value := range shape {
		var col types.KeyListEntry
		destAddr := &col
		if err := awsAwsjson11_deserializeDocumentKeyListEntry(&destAddr, value); err != nil {
			return err
		}
		col = *destAddr
		cv = append(cv, col)

	}
	*v = cv
	return nil
}

func awsAwsjson11_deserializeDocumentKeyListEntry(v **types.KeyListEntry, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.KeyListEntry
	if *v == nil {
		sv = &types.KeyListEntry{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "KeyArn":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ArnType to be of type string, got %T instead", value)
				}
				sv.KeyArn = ptr.String(jtv)
			}

		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentKeyMetadata(v **types.KeyMetadata, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.KeyMetadata
	if *v == nil {
		sv = &types.KeyMetadata{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "Arn":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ArnType to be of type string, got %T instead", value)
				}
				sv.Arn = ptr.String(jtv)
			}

		case "AWSAccountId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected AWSAccountIdType to be of type string, got %T instead", value)
				}
				sv.AWSAccountId = ptr.String(jtv)
			}

		case "CloudHsmClusterId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CloudHsmClusterIdType to be of type string, got %T instead", value)
				}
				sv.CloudHsmClusterId = ptr.String(jtv)
			}

		case "CreationDate":
			if value != nil {
				switch jtv := value.(type) {
				case json.Number:
					f64, err := jtv.Float64()
					if err != nil {
						return err
					}
					sv.CreationDate = ptr.Time(smithytime.ParseEpochSeconds(f64))

				default:
					return fmt.Errorf("expected DateType to be a JSON Number, got %T instead", value)

				}
			}

		case "CustomerMasterKeySpec":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CustomerMasterKeySpec to be of type string, got %T instead", value)
				}
				sv.CustomerMasterKeySpec = types.CustomerMasterKeySpec(jtv)
			}

		case "CustomKeyStoreId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CustomKeyStoreIdType to be of type string, got %T instead", value)
				}
				sv.CustomKeyStoreId = ptr.String(jtv)
			}

		case "DeletionDate":
			if value != nil {
				switch jtv := value.(type) {
				case json.Number:
					f64, err := jtv.Float64()
					if err != nil {
						return err
					}
					sv.DeletionDate = ptr.Time(smithytime.ParseEpochSeconds(f64))

				default:
					return fmt.Errorf("expected DateType to be a JSON Number, got %T instead", value)

				}
			}

		case "Description":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected DescriptionType to be of type string, got %T instead", value)
				}
				sv.Description = ptr.String(jtv)
			}

		case "Enabled":
			if value != nil {
				jtv, ok := value.(bool)
				if !ok {
					return fmt.Errorf("expected BooleanType to be of type *bool, got %T instead", value)
				}
				sv.Enabled = jtv
			}

		case "EncryptionAlgorithms":
			if err := awsAwsjson11_deserializeDocumentEncryptionAlgorithmSpecList(&sv.EncryptionAlgorithms, value); err != nil {
				return err
			}

		case "ExpirationModel":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ExpirationModelType to be of type string, got %T instead", value)
				}
				sv.ExpirationModel = types.ExpirationModelType(jtv)
			}

		case "KeyAgreementAlgorithms":
			if err := awsAwsjson11_deserializeDocumentKeyAgreementAlgorithmSpecList(&sv.KeyAgreementAlgorithms, value); err != nil {
				return err
			}

		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		case "KeyManager":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyManagerType to be of type string, got %T instead", value)
				}
				sv.KeyManager = types.KeyManagerType(jtv)
			}

		case "KeySpec":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeySpec to be of type string, got %T instead", value)
				}
				sv.KeySpec = types.KeySpec(jtv)
			}

		case "KeyState":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyState to be of type string, got %T instead", value)
				}
				sv.KeyState = types.KeyState(jtv)
			}

		case "KeyUsage":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyUsageType to be of type string, got %T instead", value)
				}
				sv.KeyUsage = types.KeyUsageType(jtv)
			}

		case "MacAlgorithms":
			if err := awsAwsjson11_deserializeDocumentMacAlgorithmSpecList(&sv.MacAlgorithms, value); err != nil {
				return err
			}

		case "MultiRegion":
			if value != nil {
				jtv, ok := value.(bool)
				if !ok {
					return fmt.Errorf("expected NullableBooleanType to be of type *bool, got %T instead", value)
				}
				sv.MultiRegion = ptr.Bool(jtv)
			}

		case "MultiRegionConfiguration":
			if err := awsAwsjson11_deserializeDocumentMultiRegionConfiguration(&sv.MultiRegionConfiguration, value); err != nil {
				return err
			}

		case "Origin":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected OriginType to be of type string, got %T instead", value)
				}
				sv.Origin = types.OriginType(jtv)
			}

		case "PendingDeletionWindowInDays":
			if value != nil {
				jtv, ok := value.(json.Number)
				if !ok {
					return fmt.Errorf("expected PendingWindowInDaysType to be json.Number, got %T instead", value)
				}
				i64, err := jtv.Int64()
				if err != nil {
					return err
				}
				sv.PendingDeletionWindowInDays = ptr.Int32(int32(i64))
			}

		case "SigningAlgorithms":
			if err := awsAwsjson11_deserializeDocumentSigningAlgorithmSpecList(&sv.SigningAlgorithms, value); err != nil {
				return err
			}

		case "ValidTo":
			if value != nil {
				switch jtv := value.(type) {
				case json.Number:
					f64, err := jtv.Float64()
					if err != nil {
						return err
					}
					sv.ValidTo = ptr.Time(smithytime.ParseEpochSeconds(f64))

				default:
					return fmt.Errorf("expected DateType to be a JSON Number, got %T instead", value)

				}
			}

		case "XksKeyConfiguration":
			if err := awsAwsjson11_deserializeDocumentXksKeyConfigurationType(&sv.XksKeyConfiguration, value); err != nil {
				return err
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentKeyUnavailableException(v **types.KeyUnavailableException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.KeyUnavailableException
	if *v == nil {
		sv = &types.KeyUnavailableException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentKMSInternalException(v **types.KMSInternalException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.KMSInternalException
	if *v == nil {
		sv = &types.KMSInternalException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentKMSInvalidMacException(v **types.KMSInvalidMacException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.KMSInvalidMacException
	if *v == nil {
		sv = &types.KMSInvalidMacException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentKMSInvalidSignatureException(v **types.KMSInvalidSignatureException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.KMSInvalidSignatureException
	if *v == nil {
		sv = &types.KMSInvalidSignatureException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentKMSInvalidStateException(v **types.KMSInvalidStateException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.KMSInvalidStateException
	if *v == nil {
		sv = &types.KMSInvalidStateException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentLimitExceededException(v **types.LimitExceededException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.LimitExceededException
	if *v == nil {
		sv = &types.LimitExceededException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentMacAlgorithmSpecList(v *[]types.MacAlgorithmSpec, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.([]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var cv []types.MacAlgorithmSpec
	if *v == nil {
		cv = []types.MacAlgorithmSpec{}
	} else {
		cv = *v
	}

	for _, value := range shape {
		var col types.MacAlgorithmSpec
		if value != nil {
			jtv, ok := value.(string)
			if !ok {
				return fmt.Errorf("expected MacAlgorithmSpec to be of type string, got %T instead", value)
			}
			col = types.MacAlgorithmSpec(jtv)
		}
		cv = append(cv, col)

	}
	*v = cv
	return nil
}

func awsAwsjson11_deserializeDocumentMalformedPolicyDocumentException(v **types.MalformedPolicyDocumentException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.MalformedPolicyDocumentException
	if *v == nil {
		sv = &types.MalformedPolicyDocumentException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentMultiRegionConfiguration(v **types.MultiRegionConfiguration, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.MultiRegionConfiguration
	if *v == nil {
		sv = &types.MultiRegionConfiguration{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "MultiRegionKeyType":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected MultiRegionKeyType to be of type string, got %T instead", value)
				}
				sv.MultiRegionKeyType = types.MultiRegionKeyType(jtv)
			}

		case "PrimaryKey":
			if err := awsAwsjson11_deserializeDocumentMultiRegionKey(&sv.PrimaryKey, value); err != nil {
				return err
			}

		case "ReplicaKeys":
			if err := awsAwsjson11_deserializeDocumentMultiRegionKeyList(&sv.ReplicaKeys, value); err != nil {
				return err
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentMultiRegionKey(v **types.MultiRegionKey, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.MultiRegionKey
	if *v == nil {
		sv = &types.MultiRegionKey{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "Arn":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ArnType to be of type string, got %T instead", value)
				}
				sv.Arn = ptr.String(jtv)
			}

		case "Region":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected RegionType to be of type string, got %T instead", value)
				}
				sv.Region = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentMultiRegionKeyList(v *[]types.MultiRegionKey, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.([]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var cv []types.MultiRegionKey
	if *v == nil {
		cv = []types.MultiRegionKey{}
	} else {
		cv = *v
	}

	for _, value := range shape {
		var col types.MultiRegionKey
		destAddr := &col
		if err := awsAwsjson11_deserializeDocumentMultiRegionKey(&destAddr, value); err != nil {
			return err
		}
		col = *destAddr
		cv = append(cv, col)

	}
	*v = cv
	return nil
}

func awsAwsjson11_deserializeDocumentNotFoundException(v **types.NotFoundException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.NotFoundException
	if *v == nil {
		sv = &types.NotFoundException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentPolicyNameList(v *[]string, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.([]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var cv []string
	if *v == nil {
		cv = []string{}
	} else {
		cv = *v
	}

	for _, value := range shape {
		var col string
		if value != nil {
			jtv, ok := value.(string)
			if !ok {
				return fmt.Errorf("expected PolicyNameType to be of type string, got %T instead", value)
			}
			col = jtv
		}
		cv = append(cv, col)

	}
	*v = cv
	return nil
}

func awsAwsjson11_deserializeDocumentRotationsList(v *[]types.RotationsListEntry, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.([]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var cv []types.RotationsListEntry
	if *v == nil {
		cv = []types.RotationsListEntry{}
	} else {
		cv = *v
	}

	for _, value := range shape {
		var col types.RotationsListEntry
		destAddr := &col
		if err := awsAwsjson11_deserializeDocumentRotationsListEntry(&destAddr, value); err != nil {
			return err
		}
		col = *destAddr
		cv = append(cv, col)

	}
	*v = cv
	return nil
}

func awsAwsjson11_deserializeDocumentRotationsListEntry(v **types.RotationsListEntry, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.RotationsListEntry
	if *v == nil {
		sv = &types.RotationsListEntry{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		case "RotationDate":
			if value != nil {
				switch jtv := value.(type) {
				case json.Number:
					f64, err := jtv.Float64()
					if err != nil {
						return err
					}
					sv.RotationDate = ptr.Time(smithytime.ParseEpochSeconds(f64))

				default:
					return fmt.Errorf("expected DateType to be a JSON Number, got %T instead", value)

				}
			}

		case "RotationType":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected RotationType to be of type string, got %T instead", value)
				}
				sv.RotationType = types.RotationType(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentSigningAlgorithmSpecList(v *[]types.SigningAlgorithmSpec, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.([]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var cv []types.SigningAlgorithmSpec
	if *v == nil {
		cv = []types.SigningAlgorithmSpec{}
	} else {
		cv = *v
	}

	for _, value := range shape {
		var col types.SigningAlgorithmSpec
		if value != nil {
			jtv, ok := value.(string)
			if !ok {
				return fmt.Errorf("expected SigningAlgorithmSpec to be of type string, got %T instead", value)
			}
			col = types.SigningAlgorithmSpec(jtv)
		}
		cv = append(cv, col)

	}
	*v = cv
	return nil
}

func awsAwsjson11_deserializeDocumentTag(v **types.Tag, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.Tag
	if *v == nil {
		sv = &types.Tag{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "TagKey":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected TagKeyType to be of type string, got %T instead", value)
				}
				sv.TagKey = ptr.String(jtv)
			}

		case "TagValue":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected TagValueType to be of type string, got %T instead", value)
				}
				sv.TagValue = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentTagException(v **types.TagException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.TagException
	if *v == nil {
		sv = &types.TagException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentTagList(v *[]types.Tag, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.([]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var cv []types.Tag
	if *v == nil {
		cv = []types.Tag{}
	} else {
		cv = *v
	}

	for _, value := range shape {
		var col types.Tag
		destAddr := &col
		if err := awsAwsjson11_deserializeDocumentTag(&destAddr, value); err != nil {
			return err
		}
		col = *destAddr
		cv = append(cv, col)

	}
	*v = cv
	return nil
}

func awsAwsjson11_deserializeDocumentUnsupportedOperationException(v **types.UnsupportedOperationException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.UnsupportedOperationException
	if *v == nil {
		sv = &types.UnsupportedOperationException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentXksKeyAlreadyInUseException(v **types.XksKeyAlreadyInUseException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.XksKeyAlreadyInUseException
	if *v == nil {
		sv = &types.XksKeyAlreadyInUseException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentXksKeyConfigurationType(v **types.XksKeyConfigurationType, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.XksKeyConfigurationType
	if *v == nil {
		sv = &types.XksKeyConfigurationType{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "Id":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected XksKeyIdType to be of type string, got %T instead", value)
				}
				sv.Id = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentXksKeyInvalidConfigurationException(v **types.XksKeyInvalidConfigurationException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.XksKeyInvalidConfigurationException
	if *v == nil {
		sv = &types.XksKeyInvalidConfigurationException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentXksKeyNotFoundException(v **types.XksKeyNotFoundException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.XksKeyNotFoundException
	if *v == nil {
		sv = &types.XksKeyNotFoundException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentXksProxyConfigurationType(v **types.XksProxyConfigurationType, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.XksProxyConfigurationType
	if *v == nil {
		sv = &types.XksProxyConfigurationType{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "AccessKeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected XksProxyAuthenticationAccessKeyIdType to be of type string, got %T instead", value)
				}
				sv.AccessKeyId = ptr.String(jtv)
			}

		case "Connectivity":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected XksProxyConnectivityType to be of type string, got %T instead", value)
				}
				sv.Connectivity = types.XksProxyConnectivityType(jtv)
			}

		case "UriEndpoint":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected XksProxyUriEndpointType to be of type string, got %T instead", value)
				}
				sv.UriEndpoint = ptr.String(jtv)
			}

		case "UriPath":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected XksProxyUriPathType to be of type string, got %T instead", value)
				}
				sv.UriPath = ptr.String(jtv)
			}

		case "VpcEndpointServiceName":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected XksProxyVpcEndpointServiceNameType to be of type string, got %T instead", value)
				}
				sv.VpcEndpointServiceName = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentXksProxyIncorrectAuthenticationCredentialException(v **types.XksProxyIncorrectAuthenticationCredentialException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.XksProxyIncorrectAuthenticationCredentialException
	if *v == nil {
		sv = &types.XksProxyIncorrectAuthenticationCredentialException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentXksProxyInvalidConfigurationException(v **types.XksProxyInvalidConfigurationException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.XksProxyInvalidConfigurationException
	if *v == nil {
		sv = &types.XksProxyInvalidConfigurationException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentXksProxyInvalidResponseException(v **types.XksProxyInvalidResponseException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.XksProxyInvalidResponseException
	if *v == nil {
		sv = &types.XksProxyInvalidResponseException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentXksProxyUriEndpointInUseException(v **types.XksProxyUriEndpointInUseException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.XksProxyUriEndpointInUseException
	if *v == nil {
		sv = &types.XksProxyUriEndpointInUseException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentXksProxyUriInUseException(v **types.XksProxyUriInUseException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.XksProxyUriInUseException
	if *v == nil {
		sv = &types.XksProxyUriInUseException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentXksProxyUriUnreachableException(v **types.XksProxyUriUnreachableException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.XksProxyUriUnreachableException
	if *v == nil {
		sv = &types.XksProxyUriUnreachableException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentXksProxyVpcEndpointServiceInUseException(v **types.XksProxyVpcEndpointServiceInUseException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.XksProxyVpcEndpointServiceInUseException
	if *v == nil {
		sv = &types.XksProxyVpcEndpointServiceInUseException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentXksProxyVpcEndpointServiceInvalidConfigurationException(v **types.XksProxyVpcEndpointServiceInvalidConfigurationException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.XksProxyVpcEndpointServiceInvalidConfigurationException
	if *v == nil {
		sv = &types.XksProxyVpcEndpointServiceInvalidConfigurationException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeDocumentXksProxyVpcEndpointServiceNotFoundException(v **types.XksProxyVpcEndpointServiceNotFoundException, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *types.XksProxyVpcEndpointServiceNotFoundException
	if *v == nil {
		sv = &types.XksProxyVpcEndpointServiceNotFoundException{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "message", "Message":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected ErrorMessageType to be of type string, got %T instead", value)
				}
				sv.Message = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentCancelKeyDeletionOutput(v **CancelKeyDeletionOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *CancelKeyDeletionOutput
	if *v == nil {
		sv = &CancelKeyDeletionOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentConnectCustomKeyStoreOutput(v **ConnectCustomKeyStoreOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *ConnectCustomKeyStoreOutput
	if *v == nil {
		sv = &ConnectCustomKeyStoreOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentCreateCustomKeyStoreOutput(v **CreateCustomKeyStoreOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *CreateCustomKeyStoreOutput
	if *v == nil {
		sv = &CreateCustomKeyStoreOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "CustomKeyStoreId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CustomKeyStoreIdType to be of type string, got %T instead", value)
				}
				sv.CustomKeyStoreId = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentCreateGrantOutput(v **CreateGrantOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *CreateGrantOutput
	if *v == nil {
		sv = &CreateGrantOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "GrantId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected GrantIdType to be of type string, got %T instead", value)
				}
				sv.GrantId = ptr.String(jtv)
			}

		case "GrantToken":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected GrantTokenType to be of type string, got %T instead", value)
				}
				sv.GrantToken = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentCreateKeyOutput(v **CreateKeyOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *CreateKeyOutput
	if *v == nil {
		sv = &CreateKeyOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "KeyMetadata":
			if err := awsAwsjson11_deserializeDocumentKeyMetadata(&sv.KeyMetadata, value); err != nil {
				return err
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentDecryptOutput(v **DecryptOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *DecryptOutput
	if *v == nil {
		sv = &DecryptOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "CiphertextForRecipient":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CiphertextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode CiphertextType, %w", err)
				}
				sv.CiphertextForRecipient = dv
			}

		case "EncryptionAlgorithm":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected EncryptionAlgorithmSpec to be of type string, got %T instead", value)
				}
				sv.EncryptionAlgorithm = types.EncryptionAlgorithmSpec(jtv)
			}

		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		case "Plaintext":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected PlaintextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode PlaintextType, %w", err)
				}
				sv.Plaintext = dv
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentDeleteCustomKeyStoreOutput(v **DeleteCustomKeyStoreOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *DeleteCustomKeyStoreOutput
	if *v == nil {
		sv = &DeleteCustomKeyStoreOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentDeriveSharedSecretOutput(v **DeriveSharedSecretOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *DeriveSharedSecretOutput
	if *v == nil {
		sv = &DeriveSharedSecretOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "CiphertextForRecipient":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CiphertextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode CiphertextType, %w", err)
				}
				sv.CiphertextForRecipient = dv
			}

		case "KeyAgreementAlgorithm":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyAgreementAlgorithmSpec to be of type string, got %T instead", value)
				}
				sv.KeyAgreementAlgorithm = types.KeyAgreementAlgorithmSpec(jtv)
			}

		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		case "KeyOrigin":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected OriginType to be of type string, got %T instead", value)
				}
				sv.KeyOrigin = types.OriginType(jtv)
			}

		case "SharedSecret":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected PlaintextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode PlaintextType, %w", err)
				}
				sv.SharedSecret = dv
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentDescribeCustomKeyStoresOutput(v **DescribeCustomKeyStoresOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *DescribeCustomKeyStoresOutput
	if *v == nil {
		sv = &DescribeCustomKeyStoresOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "CustomKeyStores":
			if err := awsAwsjson11_deserializeDocumentCustomKeyStoresList(&sv.CustomKeyStores, value); err != nil {
				return err
			}

		case "NextMarker":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected MarkerType to be of type string, got %T instead", value)
				}
				sv.NextMarker = ptr.String(jtv)
			}

		case "Truncated":
			if value != nil {
				jtv, ok := value.(bool)
				if !ok {
					return fmt.Errorf("expected BooleanType to be of type *bool, got %T instead", value)
				}
				sv.Truncated = jtv
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentDescribeKeyOutput(v **DescribeKeyOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *DescribeKeyOutput
	if *v == nil {
		sv = &DescribeKeyOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "KeyMetadata":
			if err := awsAwsjson11_deserializeDocumentKeyMetadata(&sv.KeyMetadata, value); err != nil {
				return err
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentDisconnectCustomKeyStoreOutput(v **DisconnectCustomKeyStoreOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *DisconnectCustomKeyStoreOutput
	if *v == nil {
		sv = &DisconnectCustomKeyStoreOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentEncryptOutput(v **EncryptOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *EncryptOutput
	if *v == nil {
		sv = &EncryptOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "CiphertextBlob":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CiphertextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode CiphertextType, %w", err)
				}
				sv.CiphertextBlob = dv
			}

		case "EncryptionAlgorithm":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected EncryptionAlgorithmSpec to be of type string, got %T instead", value)
				}
				sv.EncryptionAlgorithm = types.EncryptionAlgorithmSpec(jtv)
			}

		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentGenerateDataKeyOutput(v **GenerateDataKeyOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *GenerateDataKeyOutput
	if *v == nil {
		sv = &GenerateDataKeyOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "CiphertextBlob":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CiphertextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode CiphertextType, %w", err)
				}
				sv.CiphertextBlob = dv
			}

		case "CiphertextForRecipient":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CiphertextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode CiphertextType, %w", err)
				}
				sv.CiphertextForRecipient = dv
			}

		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		case "Plaintext":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected PlaintextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode PlaintextType, %w", err)
				}
				sv.Plaintext = dv
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentGenerateDataKeyPairOutput(v **GenerateDataKeyPairOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *GenerateDataKeyPairOutput
	if *v == nil {
		sv = &GenerateDataKeyPairOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "CiphertextForRecipient":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CiphertextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode CiphertextType, %w", err)
				}
				sv.CiphertextForRecipient = dv
			}

		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		case "KeyPairSpec":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected DataKeyPairSpec to be of type string, got %T instead", value)
				}
				sv.KeyPairSpec = types.DataKeyPairSpec(jtv)
			}

		case "PrivateKeyCiphertextBlob":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CiphertextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode CiphertextType, %w", err)
				}
				sv.PrivateKeyCiphertextBlob = dv
			}

		case "PrivateKeyPlaintext":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected PlaintextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode PlaintextType, %w", err)
				}
				sv.PrivateKeyPlaintext = dv
			}

		case "PublicKey":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected PublicKeyType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode PublicKeyType, %w", err)
				}
				sv.PublicKey = dv
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentGenerateDataKeyPairWithoutPlaintextOutput(v **GenerateDataKeyPairWithoutPlaintextOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *GenerateDataKeyPairWithoutPlaintextOutput
	if *v == nil {
		sv = &GenerateDataKeyPairWithoutPlaintextOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		case "KeyPairSpec":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected DataKeyPairSpec to be of type string, got %T instead", value)
				}
				sv.KeyPairSpec = types.DataKeyPairSpec(jtv)
			}

		case "PrivateKeyCiphertextBlob":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CiphertextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode CiphertextType, %w", err)
				}
				sv.PrivateKeyCiphertextBlob = dv
			}

		case "PublicKey":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected PublicKeyType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode PublicKeyType, %w", err)
				}
				sv.PublicKey = dv
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentGenerateDataKeyWithoutPlaintextOutput(v **GenerateDataKeyWithoutPlaintextOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *GenerateDataKeyWithoutPlaintextOutput
	if *v == nil {
		sv = &GenerateDataKeyWithoutPlaintextOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "CiphertextBlob":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CiphertextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode CiphertextType, %w", err)
				}
				sv.CiphertextBlob = dv
			}

		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentGenerateMacOutput(v **GenerateMacOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *GenerateMacOutput
	if *v == nil {
		sv = &GenerateMacOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		case "Mac":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CiphertextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode CiphertextType, %w", err)
				}
				sv.Mac = dv
			}

		case "MacAlgorithm":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected MacAlgorithmSpec to be of type string, got %T instead", value)
				}
				sv.MacAlgorithm = types.MacAlgorithmSpec(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentGenerateRandomOutput(v **GenerateRandomOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *GenerateRandomOutput
	if *v == nil {
		sv = &GenerateRandomOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "CiphertextForRecipient":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CiphertextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode CiphertextType, %w", err)
				}
				sv.CiphertextForRecipient = dv
			}

		case "Plaintext":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected PlaintextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode PlaintextType, %w", err)
				}
				sv.Plaintext = dv
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentGetKeyPolicyOutput(v **GetKeyPolicyOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *GetKeyPolicyOutput
	if *v == nil {
		sv = &GetKeyPolicyOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "Policy":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected PolicyType to be of type string, got %T instead", value)
				}
				sv.Policy = ptr.String(jtv)
			}

		case "PolicyName":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected PolicyNameType to be of type string, got %T instead", value)
				}
				sv.PolicyName = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentGetKeyRotationStatusOutput(v **GetKeyRotationStatusOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *GetKeyRotationStatusOutput
	if *v == nil {
		sv = &GetKeyRotationStatusOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		case "KeyRotationEnabled":
			if value != nil {
				jtv, ok := value.(bool)
				if !ok {
					return fmt.Errorf("expected BooleanType to be of type *bool, got %T instead", value)
				}
				sv.KeyRotationEnabled = jtv
			}

		case "NextRotationDate":
			if value != nil {
				switch jtv := value.(type) {
				case json.Number:
					f64, err := jtv.Float64()
					if err != nil {
						return err
					}
					sv.NextRotationDate = ptr.Time(smithytime.ParseEpochSeconds(f64))

				default:
					return fmt.Errorf("expected DateType to be a JSON Number, got %T instead", value)

				}
			}

		case "OnDemandRotationStartDate":
			if value != nil {
				switch jtv := value.(type) {
				case json.Number:
					f64, err := jtv.Float64()
					if err != nil {
						return err
					}
					sv.OnDemandRotationStartDate = ptr.Time(smithytime.ParseEpochSeconds(f64))

				default:
					return fmt.Errorf("expected DateType to be a JSON Number, got %T instead", value)

				}
			}

		case "RotationPeriodInDays":
			if value != nil {
				jtv, ok := value.(json.Number)
				if !ok {
					return fmt.Errorf("expected RotationPeriodInDaysType to be json.Number, got %T instead", value)
				}
				i64, err := jtv.Int64()
				if err != nil {
					return err
				}
				sv.RotationPeriodInDays = ptr.Int32(int32(i64))
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentGetParametersForImportOutput(v **GetParametersForImportOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *GetParametersForImportOutput
	if *v == nil {
		sv = &GetParametersForImportOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "ImportToken":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CiphertextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode CiphertextType, %w", err)
				}
				sv.ImportToken = dv
			}

		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		case "ParametersValidTo":
			if value != nil {
				switch jtv := value.(type) {
				case json.Number:
					f64, err := jtv.Float64()
					if err != nil {
						return err
					}
					sv.ParametersValidTo = ptr.Time(smithytime.ParseEpochSeconds(f64))

				default:
					return fmt.Errorf("expected DateType to be a JSON Number, got %T instead", value)

				}
			}

		case "PublicKey":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected PlaintextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode PlaintextType, %w", err)
				}
				sv.PublicKey = dv
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentGetPublicKeyOutput(v **GetPublicKeyOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *GetPublicKeyOutput
	if *v == nil {
		sv = &GetPublicKeyOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "CustomerMasterKeySpec":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CustomerMasterKeySpec to be of type string, got %T instead", value)
				}
				sv.CustomerMasterKeySpec = types.CustomerMasterKeySpec(jtv)
			}

		case "EncryptionAlgorithms":
			if err := awsAwsjson11_deserializeDocumentEncryptionAlgorithmSpecList(&sv.EncryptionAlgorithms, value); err != nil {
				return err
			}

		case "KeyAgreementAlgorithms":
			if err := awsAwsjson11_deserializeDocumentKeyAgreementAlgorithmSpecList(&sv.KeyAgreementAlgorithms, value); err != nil {
				return err
			}

		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		case "KeySpec":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeySpec to be of type string, got %T instead", value)
				}
				sv.KeySpec = types.KeySpec(jtv)
			}

		case "KeyUsage":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyUsageType to be of type string, got %T instead", value)
				}
				sv.KeyUsage = types.KeyUsageType(jtv)
			}

		case "PublicKey":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected PublicKeyType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode PublicKeyType, %w", err)
				}
				sv.PublicKey = dv
			}

		case "SigningAlgorithms":
			if err := awsAwsjson11_deserializeDocumentSigningAlgorithmSpecList(&sv.SigningAlgorithms, value); err != nil {
				return err
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentImportKeyMaterialOutput(v **ImportKeyMaterialOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *ImportKeyMaterialOutput
	if *v == nil {
		sv = &ImportKeyMaterialOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentListAliasesOutput(v **ListAliasesOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *ListAliasesOutput
	if *v == nil {
		sv = &ListAliasesOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "Aliases":
			if err := awsAwsjson11_deserializeDocumentAliasList(&sv.Aliases, value); err != nil {
				return err
			}

		case "NextMarker":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected MarkerType to be of type string, got %T instead", value)
				}
				sv.NextMarker = ptr.String(jtv)
			}

		case "Truncated":
			if value != nil {
				jtv, ok := value.(bool)
				if !ok {
					return fmt.Errorf("expected BooleanType to be of type *bool, got %T instead", value)
				}
				sv.Truncated = jtv
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentListGrantsOutput(v **ListGrantsOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *ListGrantsOutput
	if *v == nil {
		sv = &ListGrantsOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "Grants":
			if err := awsAwsjson11_deserializeDocumentGrantList(&sv.Grants, value); err != nil {
				return err
			}

		case "NextMarker":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected MarkerType to be of type string, got %T instead", value)
				}
				sv.NextMarker = ptr.String(jtv)
			}

		case "Truncated":
			if value != nil {
				jtv, ok := value.(bool)
				if !ok {
					return fmt.Errorf("expected BooleanType to be of type *bool, got %T instead", value)
				}
				sv.Truncated = jtv
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentListKeyPoliciesOutput(v **ListKeyPoliciesOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *ListKeyPoliciesOutput
	if *v == nil {
		sv = &ListKeyPoliciesOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "NextMarker":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected MarkerType to be of type string, got %T instead", value)
				}
				sv.NextMarker = ptr.String(jtv)
			}

		case "PolicyNames":
			if err := awsAwsjson11_deserializeDocumentPolicyNameList(&sv.PolicyNames, value); err != nil {
				return err
			}

		case "Truncated":
			if value != nil {
				jtv, ok := value.(bool)
				if !ok {
					return fmt.Errorf("expected BooleanType to be of type *bool, got %T instead", value)
				}
				sv.Truncated = jtv
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentListKeyRotationsOutput(v **ListKeyRotationsOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *ListKeyRotationsOutput
	if *v == nil {
		sv = &ListKeyRotationsOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "NextMarker":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected MarkerType to be of type string, got %T instead", value)
				}
				sv.NextMarker = ptr.String(jtv)
			}

		case "Rotations":
			if err := awsAwsjson11_deserializeDocumentRotationsList(&sv.Rotations, value); err != nil {
				return err
			}

		case "Truncated":
			if value != nil {
				jtv, ok := value.(bool)
				if !ok {
					return fmt.Errorf("expected BooleanType to be of type *bool, got %T instead", value)
				}
				sv.Truncated = jtv
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentListKeysOutput(v **ListKeysOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *ListKeysOutput
	if *v == nil {
		sv = &ListKeysOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "Keys":
			if err := awsAwsjson11_deserializeDocumentKeyList(&sv.Keys, value); err != nil {
				return err
			}

		case "NextMarker":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected MarkerType to be of type string, got %T instead", value)
				}
				sv.NextMarker = ptr.String(jtv)
			}

		case "Truncated":
			if value != nil {
				jtv, ok := value.(bool)
				if !ok {
					return fmt.Errorf("expected BooleanType to be of type *bool, got %T instead", value)
				}
				sv.Truncated = jtv
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentListResourceTagsOutput(v **ListResourceTagsOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *ListResourceTagsOutput
	if *v == nil {
		sv = &ListResourceTagsOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "NextMarker":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected MarkerType to be of type string, got %T instead", value)
				}
				sv.NextMarker = ptr.String(jtv)
			}

		case "Tags":
			if err := awsAwsjson11_deserializeDocumentTagList(&sv.Tags, value); err != nil {
				return err
			}

		case "Truncated":
			if value != nil {
				jtv, ok := value.(bool)
				if !ok {
					return fmt.Errorf("expected BooleanType to be of type *bool, got %T instead", value)
				}
				sv.Truncated = jtv
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentListRetirableGrantsOutput(v **ListRetirableGrantsOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *ListRetirableGrantsOutput
	if *v == nil {
		sv = &ListRetirableGrantsOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "Grants":
			if err := awsAwsjson11_deserializeDocumentGrantList(&sv.Grants, value); err != nil {
				return err
			}

		case "NextMarker":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected MarkerType to be of type string, got %T instead", value)
				}
				sv.NextMarker = ptr.String(jtv)
			}

		case "Truncated":
			if value != nil {
				jtv, ok := value.(bool)
				if !ok {
					return fmt.Errorf("expected BooleanType to be of type *bool, got %T instead", value)
				}
				sv.Truncated = jtv
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentReEncryptOutput(v **ReEncryptOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *ReEncryptOutput
	if *v == nil {
		sv = &ReEncryptOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "CiphertextBlob":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CiphertextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode CiphertextType, %w", err)
				}
				sv.CiphertextBlob = dv
			}

		case "DestinationEncryptionAlgorithm":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected EncryptionAlgorithmSpec to be of type string, got %T instead", value)
				}
				sv.DestinationEncryptionAlgorithm = types.EncryptionAlgorithmSpec(jtv)
			}

		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		case "SourceEncryptionAlgorithm":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected EncryptionAlgorithmSpec to be of type string, got %T instead", value)
				}
				sv.SourceEncryptionAlgorithm = types.EncryptionAlgorithmSpec(jtv)
			}

		case "SourceKeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.SourceKeyId = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentReplicateKeyOutput(v **ReplicateKeyOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *ReplicateKeyOutput
	if *v == nil {
		sv = &ReplicateKeyOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "ReplicaKeyMetadata":
			if err := awsAwsjson11_deserializeDocumentKeyMetadata(&sv.ReplicaKeyMetadata, value); err != nil {
				return err
			}

		case "ReplicaPolicy":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected PolicyType to be of type string, got %T instead", value)
				}
				sv.ReplicaPolicy = ptr.String(jtv)
			}

		case "ReplicaTags":
			if err := awsAwsjson11_deserializeDocumentTagList(&sv.ReplicaTags, value); err != nil {
				return err
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentRotateKeyOnDemandOutput(v **RotateKeyOnDemandOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *RotateKeyOnDemandOutput
	if *v == nil {
		sv = &RotateKeyOnDemandOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentScheduleKeyDeletionOutput(v **ScheduleKeyDeletionOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *ScheduleKeyDeletionOutput
	if *v == nil {
		sv = &ScheduleKeyDeletionOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "DeletionDate":
			if value != nil {
				switch jtv := value.(type) {
				case json.Number:
					f64, err := jtv.Float64()
					if err != nil {
						return err
					}
					sv.DeletionDate = ptr.Time(smithytime.ParseEpochSeconds(f64))

				default:
					return fmt.Errorf("expected DateType to be a JSON Number, got %T instead", value)

				}
			}

		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		case "KeyState":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyState to be of type string, got %T instead", value)
				}
				sv.KeyState = types.KeyState(jtv)
			}

		case "PendingWindowInDays":
			if value != nil {
				jtv, ok := value.(json.Number)
				if !ok {
					return fmt.Errorf("expected PendingWindowInDaysType to be json.Number, got %T instead", value)
				}
				i64, err := jtv.Int64()
				if err != nil {
					return err
				}
				sv.PendingWindowInDays = ptr.Int32(int32(i64))
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentSignOutput(v **SignOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *SignOutput
	if *v == nil {
		sv = &SignOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		case "Signature":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected CiphertextType to be []byte, got %T instead", value)
				}
				dv, err := base64.StdEncoding.DecodeString(jtv)
				if err != nil {
					return fmt.Errorf("failed to base64 decode CiphertextType, %w", err)
				}
				sv.Signature = dv
			}

		case "SigningAlgorithm":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected SigningAlgorithmSpec to be of type string, got %T instead", value)
				}
				sv.SigningAlgorithm = types.SigningAlgorithmSpec(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentUpdateCustomKeyStoreOutput(v **UpdateCustomKeyStoreOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *UpdateCustomKeyStoreOutput
	if *v == nil {
		sv = &UpdateCustomKeyStoreOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentVerifyMacOutput(v **VerifyMacOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *VerifyMacOutput
	if *v == nil {
		sv = &VerifyMacOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		case "MacAlgorithm":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected MacAlgorithmSpec to be of type string, got %T instead", value)
				}
				sv.MacAlgorithm = types.MacAlgorithmSpec(jtv)
			}

		case "MacValid":
			if value != nil {
				jtv, ok := value.(bool)
				if !ok {
					return fmt.Errorf("expected BooleanType to be of type *bool, got %T instead", value)
				}
				sv.MacValid = jtv
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

func awsAwsjson11_deserializeOpDocumentVerifyOutput(v **VerifyOutput, value interface{}) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	if value == nil {
		return nil
	}

	shape, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("unexpected JSON type %v", value)
	}

	var sv *VerifyOutput
	if *v == nil {
		sv = &VerifyOutput{}
	} else {
		sv = *v
	}

	for key, value := range shape {
		switch key {
		case "KeyId":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected KeyIdType to be of type string, got %T instead", value)
				}
				sv.KeyId = ptr.String(jtv)
			}

		case "SignatureValid":
			if value != nil {
				jtv, ok := value.(bool)
				if !ok {
					return fmt.Errorf("expected BooleanType to be of type *bool, got %T instead", value)
				}
				sv.SignatureValid = jtv
			}

		case "SigningAlgorithm":
			if value != nil {
				jtv, ok := value.(string)
				if !ok {
					return fmt.Errorf("expected SigningAlgorithmSpec to be of type string, got %T instead", value)
				}
				sv.SigningAlgorithm = types.SigningAlgorithmSpec(jtv)
			}

		default:
			_, _ = key, value

		}
	}
	*v = sv
	return nil
}

type protocolErrorInfo struct {
	Type    string `json:"__type"`
	Message string
	Code    any // nonstandard for awsjson but some services do present the type here
}

func getProtocolErrorInfo(decoder *json.Decoder) (protocolErrorInfo, error) {
	var errInfo protocolErrorInfo
	if err := decoder.Decode(&errInfo); err != nil {
		if err == io.EOF {
			return errInfo, nil
		}
		return errInfo, err
	}

	return errInfo, nil
}

func resolveProtocolErrorType(headerType string, bodyInfo protocolErrorInfo) (string, bool) {
	if len(headerType) != 0 {
		return headerType, true
	} else if len(bodyInfo.Type) != 0 {
		return bodyInfo.Type, true
	} else if code, ok := bodyInfo.Code.(string); ok && len(code) != 0 {
		return code, true
	}
	return "", false
}
