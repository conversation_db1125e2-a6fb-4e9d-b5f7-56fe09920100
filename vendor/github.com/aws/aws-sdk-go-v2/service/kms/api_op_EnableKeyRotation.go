// Code generated by smithy-go-codegen DO NOT EDIT.

package kms

import (
	"context"
	"fmt"
	awsmiddleware "github.com/aws/aws-sdk-go-v2/aws/middleware"
	"github.com/aws/smithy-go/middleware"
	smithyhttp "github.com/aws/smithy-go/transport/http"
)

// Enables [automatic rotation of the key material] of the specified symmetric encryption KMS key.
//
// By default, when you enable automatic rotation of a [customer managed KMS key], K<PERSON> rotates the key
// material of the KMS key one year (approximately 365 days) from the enable date
// and every year thereafter. You can use the optional RotationPeriodInDays
// parameter to specify a custom rotation period when you enable key rotation, or
// you can use RotationPeriodInDays to modify the rotation period of a key that
// you previously enabled automatic key rotation on.
//
// You can monitor rotation of the key material for your KMS keys in CloudTrail
// and Amazon CloudWatch. To disable rotation of the key material in a customer
// managed KMS key, use the DisableKeyRotationoperation. You can use the GetKeyRotationStatus operation to identify any
// in progress rotations. You can use the ListKeyRotationsoperation to view the details of
// completed rotations.
//
// Automatic key rotation is supported only on [symmetric encryption KMS keys]. You cannot enable automatic
// rotation of [asymmetric KMS keys], [HMAC KMS keys], KMS keys with [imported key material], or KMS keys in a [custom key store]. To enable or disable
// automatic rotation of a set of related [multi-Region keys], set the property on the primary key.
//
// You cannot enable or disable automatic rotation of [Amazon Web Services managed KMS keys]. KMS always rotates the key
// material of Amazon Web Services managed keys every year. Rotation of [Amazon Web Services owned KMS keys]is managed
// by the Amazon Web Services service that owns the key.
//
// In May 2022, KMS changed the rotation schedule for Amazon Web Services managed
// keys from every three years (approximately 1,095 days) to every year
// (approximately 365 days).
//
// New Amazon Web Services managed keys are automatically rotated one year after
// they are created, and approximately every year thereafter.
//
// Existing Amazon Web Services managed keys are automatically rotated one year
// after their most recent rotation, and every year thereafter.
//
// The KMS key that you use for this operation must be in a compatible key state.
// For details, see [Key states of KMS keys]in the Key Management Service Developer Guide.
//
// Cross-account use: No. You cannot perform this operation on a KMS key in a
// different Amazon Web Services account.
//
// Required permissions: [kms:EnableKeyRotation] (key policy)
//
// Related operations:
//
// # DisableKeyRotation
//
// # GetKeyRotationStatus
//
// # ListKeyRotations
//
// RotateKeyOnDemand
//
//   - You can perform on-demand (RotateKeyOnDemand ) rotation of the key material in customer
//     managed KMS keys, regardless of whether or not automatic key rotation is
//     enabled.
//
// Eventual consistency: The KMS API follows an eventual consistency model. For
// more information, see [KMS eventual consistency].
//
// [kms:EnableKeyRotation]: https://docs.aws.amazon.com/kms/latest/developerguide/kms-api-permissions-reference.html
// [Amazon Web Services owned KMS keys]: https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#aws-owned-cmk
// [multi-Region keys]: https://docs.aws.amazon.com/kms/latest/developerguide/multi-region-keys-manage.html#multi-region-rotate
// [KMS eventual consistency]: https://docs.aws.amazon.com/kms/latest/developerguide/programming-eventual-consistency.html
// [imported key material]: https://docs.aws.amazon.com/kms/latest/developerguide/importing-keys.html
// [Key states of KMS keys]: https://docs.aws.amazon.com/kms/latest/developerguide/key-state.html
// [HMAC KMS keys]: https://docs.aws.amazon.com/kms/latest/developerguide/hmac.html
// [Amazon Web Services managed KMS keys]: https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#aws-managed-cmk
// [customer managed KMS key]: https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#customer-cmk
// [automatic rotation of the key material]: https://docs.aws.amazon.com/kms/latest/developerguide/rotate-keys.html#rotating-keys-enable-disable
// [asymmetric KMS keys]: https://docs.aws.amazon.com/kms/latest/developerguide/symmetric-asymmetric.html
// [symmetric encryption KMS keys]: https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#symmetric-cmks
// [custom key store]: https://docs.aws.amazon.com/kms/latest/developerguide/custom-key-store-overview.html
func (c *Client) EnableKeyRotation(ctx context.Context, params *EnableKeyRotationInput, optFns ...func(*Options)) (*EnableKeyRotationOutput, error) {
	if params == nil {
		params = &EnableKeyRotationInput{}
	}

	result, metadata, err := c.invokeOperation(ctx, "EnableKeyRotation", params, optFns, c.addOperationEnableKeyRotationMiddlewares)
	if err != nil {
		return nil, err
	}

	out := result.(*EnableKeyRotationOutput)
	out.ResultMetadata = metadata
	return out, nil
}

type EnableKeyRotationInput struct {

	// Identifies a symmetric encryption KMS key. You cannot enable automatic rotation
	// of [asymmetric KMS keys], [HMAC KMS keys], KMS keys with [imported key material], or KMS keys in a [custom key store]. To enable or disable automatic
	// rotation of a set of related [multi-Region keys], set the property on the primary key.
	//
	// Specify the key ID or key ARN of the KMS key.
	//
	// For example:
	//
	//   - Key ID: 1234abcd-12ab-34cd-56ef-1234567890ab
	//
	//   - Key ARN:
	//   arn:aws:kms:us-east-2:111122223333:key/1234abcd-12ab-34cd-56ef-1234567890ab
	//
	// To get the key ID and key ARN for a KMS key, use ListKeys or DescribeKey.
	//
	// [imported key material]: https://docs.aws.amazon.com/kms/latest/developerguide/importing-keys.html
	// [HMAC KMS keys]: https://docs.aws.amazon.com/kms/latest/developerguide/hmac.html
	// [asymmetric KMS keys]: https://docs.aws.amazon.com/kms/latest/developerguide/symmetric-asymmetric.html
	// [multi-Region keys]: https://docs.aws.amazon.com/kms/latest/developerguide/multi-region-keys-manage.html#multi-region-rotate
	// [custom key store]: https://docs.aws.amazon.com/kms/latest/developerguide/custom-key-store-overview.html
	//
	// This member is required.
	KeyId *string

	// Use this parameter to specify a custom period of time between each rotation
	// date. If no value is specified, the default value is 365 days.
	//
	// The rotation period defines the number of days after you enable automatic key
	// rotation that KMS will rotate your key material, and the number of days between
	// each automatic rotation thereafter.
	//
	// You can use the [kms:RotationPeriodInDays]kms:RotationPeriodInDays condition key to further constrain the
	// values that principals can specify in the RotationPeriodInDays parameter.
	//
	// [kms:RotationPeriodInDays]: https://docs.aws.amazon.com/kms/latest/developerguide/conditions-kms.html#conditions-kms-rotation-period-in-days
	RotationPeriodInDays *int32

	noSmithyDocumentSerde
}

type EnableKeyRotationOutput struct {
	// Metadata pertaining to the operation's result.
	ResultMetadata middleware.Metadata

	noSmithyDocumentSerde
}

func (c *Client) addOperationEnableKeyRotationMiddlewares(stack *middleware.Stack, options Options) (err error) {
	if err := stack.Serialize.Add(&setOperationInputMiddleware{}, middleware.After); err != nil {
		return err
	}
	err = stack.Serialize.Add(&awsAwsjson11_serializeOpEnableKeyRotation{}, middleware.After)
	if err != nil {
		return err
	}
	err = stack.Deserialize.Add(&awsAwsjson11_deserializeOpEnableKeyRotation{}, middleware.After)
	if err != nil {
		return err
	}
	if err := addProtocolFinalizerMiddlewares(stack, options, "EnableKeyRotation"); err != nil {
		return fmt.Errorf("add protocol finalizers: %v", err)
	}

	if err = addlegacyEndpointContextSetter(stack, options); err != nil {
		return err
	}
	if err = addSetLoggerMiddleware(stack, options); err != nil {
		return err
	}
	if err = addClientRequestID(stack); err != nil {
		return err
	}
	if err = addComputeContentLength(stack); err != nil {
		return err
	}
	if err = addResolveEndpointMiddleware(stack, options); err != nil {
		return err
	}
	if err = addComputePayloadSHA256(stack); err != nil {
		return err
	}
	if err = addRetry(stack, options); err != nil {
		return err
	}
	if err = addRawResponseToMetadata(stack); err != nil {
		return err
	}
	if err = addRecordResponseTiming(stack); err != nil {
		return err
	}
	if err = addSpanRetryLoop(stack, options); err != nil {
		return err
	}
	if err = addClientUserAgent(stack, options); err != nil {
		return err
	}
	if err = smithyhttp.AddErrorCloseResponseBodyMiddleware(stack); err != nil {
		return err
	}
	if err = smithyhttp.AddCloseResponseBodyMiddleware(stack); err != nil {
		return err
	}
	if err = addSetLegacyContextSigningOptionsMiddleware(stack); err != nil {
		return err
	}
	if err = addTimeOffsetBuild(stack, c); err != nil {
		return err
	}
	if err = addUserAgentRetryMode(stack, options); err != nil {
		return err
	}
	if err = addOpEnableKeyRotationValidationMiddleware(stack); err != nil {
		return err
	}
	if err = stack.Initialize.Add(newServiceMetadataMiddleware_opEnableKeyRotation(options.Region), middleware.Before); err != nil {
		return err
	}
	if err = addRecursionDetection(stack); err != nil {
		return err
	}
	if err = addRequestIDRetrieverMiddleware(stack); err != nil {
		return err
	}
	if err = addResponseErrorMiddleware(stack); err != nil {
		return err
	}
	if err = addRequestResponseLogging(stack, options); err != nil {
		return err
	}
	if err = addDisableHTTPSMiddleware(stack, options); err != nil {
		return err
	}
	if err = addSpanInitializeStart(stack); err != nil {
		return err
	}
	if err = addSpanInitializeEnd(stack); err != nil {
		return err
	}
	if err = addSpanBuildRequestStart(stack); err != nil {
		return err
	}
	if err = addSpanBuildRequestEnd(stack); err != nil {
		return err
	}
	return nil
}

func newServiceMetadataMiddleware_opEnableKeyRotation(region string) *awsmiddleware.RegisterServiceMetadata {
	return &awsmiddleware.RegisterServiceMetadata{
		Region:        region,
		ServiceID:     ServiceID,
		OperationName: "EnableKeyRotation",
	}
}
