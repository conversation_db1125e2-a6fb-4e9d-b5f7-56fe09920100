# v1.12.2 (2025-01-24)

* **Dependency Update**: Upgrade to smithy-go v1.22.2.

# v1.12.1 (2024-11-18)

* **Dependency Update**: Update to smithy-go v1.22.1.

# v1.12.0 (2024-10-04)

* **Feature**: Add support for HTTP client metrics.

# v1.11.5 (2024-09-20)

* No change notes available for this release.

# v1.11.4 (2024-08-15)

* **Dependency Update**: Bump minimum Go version to 1.21.

# v1.11.3 (2024-06-28)

* No change notes available for this release.

# v1.11.2 (2024-03-29)

* No change notes available for this release.

# v1.11.1 (2024-02-21)

* No change notes available for this release.

# v1.11.0 (2024-02-13)

* **Feature**: Bump minimum Go version to 1.20 per our language support policy.

# v1.10.4 (2023-12-07)

* No change notes available for this release.

# v1.10.3 (2023-11-30)

* No change notes available for this release.

# v1.10.2 (2023-11-29)

* No change notes available for this release.

# v1.10.1 (2023-11-15)

* No change notes available for this release.

# v1.10.0 (2023-10-31)

* **Feature**: **BREAKING CHANGE**: Bump minimum go version to 1.19 per the revised [go version support policy](https://aws.amazon.com/blogs/developer/aws-sdk-for-go-aligns-with-go-release-policy-on-supported-runtimes/).

# v1.9.15 (2023-10-06)

* No change notes available for this release.

# v1.9.14 (2023-08-18)

* No change notes available for this release.

# v1.9.13 (2023-08-07)

* No change notes available for this release.

# v1.9.12 (2023-07-31)

* No change notes available for this release.

# v1.9.11 (2022-12-02)

* No change notes available for this release.

# v1.9.10 (2022-10-24)

* No change notes available for this release.

# v1.9.9 (2022-09-14)

* No change notes available for this release.

# v1.9.8 (2022-09-02)

* No change notes available for this release.

# v1.9.7 (2022-08-31)

* No change notes available for this release.

# v1.9.6 (2022-08-29)

* No change notes available for this release.

# v1.9.5 (2022-08-11)

* No change notes available for this release.

# v1.9.4 (2022-08-09)

* No change notes available for this release.

# v1.9.3 (2022-06-29)

* No change notes available for this release.

# v1.9.2 (2022-06-07)

* No change notes available for this release.

# v1.9.1 (2022-03-24)

* No change notes available for this release.

# v1.9.0 (2022-03-08)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version

# v1.8.0 (2022-02-24)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version

# v1.7.0 (2022-01-14)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version

# v1.6.0 (2022-01-07)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version

# v1.5.0 (2021-11-06)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version

# v1.4.0 (2021-10-21)

* **Feature**: Updated  to latest version

# v1.3.0 (2021-08-27)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version

# v1.2.2 (2021-08-04)

* **Dependency Update**: Updated `github.com/aws/smithy-go` to latest version.

# v1.2.1 (2021-07-15)

* **Dependency Update**: Updated `github.com/aws/smithy-go` to latest version

# v1.2.0 (2021-06-25)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version

# v1.1.0 (2021-05-14)

* **Feature**: Constant has been added to modules to enable runtime version inspection for reporting.

