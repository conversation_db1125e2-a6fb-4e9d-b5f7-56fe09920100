# v1.12.12 (2025-01-31)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.11 (2025-01-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.10 (2025-01-24)

* **Dependency Update**: Updated to the latest SDK module versions
* **Dependency Update**: Upgrade to smithy-go v1.22.2.

# v1.12.9 (2025-01-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.8 (2025-01-09)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.7 (2024-12-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.6 (2024-12-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.5 (2024-11-18)

* **Dependency Update**: Update to smithy-go v1.22.1.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.4 (2024-11-06)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.3 (2024-10-28)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.2 (2024-10-08)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.1 (2024-10-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.12.0 (2024-10-04)

* **Feature**: Add support for HTTP client metrics.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.20 (2024-09-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.19 (2024-09-03)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.18 (2024-08-15)

* **Dependency Update**: Bump minimum Go version to 1.21.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.17 (2024-07-10.2)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.16 (2024-07-10)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.15 (2024-06-28)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.14 (2024-06-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.13 (2024-06-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.12 (2024-06-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.11 (2024-06-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.10 (2024-06-03)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.9 (2024-05-16)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.8 (2024-05-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.7 (2024-03-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.6 (2024-03-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.5 (2024-03-07)

* **Bug Fix**: Remove dependency on go-cmp.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.4 (2024-03-05)

* **Bug Fix**: Restore typo'd API `AddAsIsInternalPresigingMiddleware` as an alias for backwards compatibility.

# v1.11.3 (2024-03-04)

* **Bug Fix**: Correct a typo in internal AddAsIsPresigningMiddleware API.

# v1.11.2 (2024-02-23)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.1 (2024-02-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.11.0 (2024-02-13)

* **Feature**: Bump minimum Go version to 1.20 per our language support policy.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.10.10 (2024-01-04)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.10.9 (2023-12-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.10.8 (2023-12-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.10.7 (2023-11-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.10.6 (2023-11-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.10.5 (2023-11-28.2)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.10.4 (2023-11-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.10.3 (2023-11-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.10.2 (2023-11-09)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.10.1 (2023-11-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.10.0 (2023-10-31)

* **Feature**: **BREAKING CHANGE**: Bump minimum go version to 1.19 per the revised [go version support policy](https://aws.amazon.com/blogs/developer/aws-sdk-for-go-aligns-with-go-release-policy-on-supported-runtimes/).
* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.37 (2023-10-12)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.36 (2023-10-06)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.35 (2023-08-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.34 (2023-08-18)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.33 (2023-08-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.32 (2023-08-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.31 (2023-07-31)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.30 (2023-07-28)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.29 (2023-07-13)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.28 (2023-06-13)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.27 (2023-04-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.26 (2023-04-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.25 (2023-03-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.24 (2023-03-10)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.23 (2023-02-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.22 (2023-02-03)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.21 (2022-12-15)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.20 (2022-12-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.19 (2022-10-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.18 (2022-10-21)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.17 (2022-09-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.16 (2022-09-14)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.15 (2022-09-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.14 (2022-08-31)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.13 (2022-08-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.12 (2022-08-11)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.11 (2022-08-09)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.10 (2022-08-08)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.9 (2022-08-01)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.8 (2022-07-05)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.7 (2022-06-29)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.6 (2022-06-07)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.5 (2022-05-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.4 (2022-04-25)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.3 (2022-03-30)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.2 (2022-03-24)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.1 (2022-03-23)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.9.0 (2022-03-08)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.8.0 (2022-02-24)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.7.0 (2022-01-14)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.6.0 (2022-01-07)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.5.2 (2021-12-02)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.5.1 (2021-11-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.5.0 (2021-11-06)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.4.0 (2021-10-21)

* **Feature**: Updated  to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.2 (2021-10-11)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.1 (2021-09-17)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.3.0 (2021-08-27)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.3 (2021-08-19)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.2 (2021-08-04)

* **Dependency Update**: Updated `github.com/aws/smithy-go` to latest version.
* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.1 (2021-07-15)

* **Dependency Update**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.2.0 (2021-06-25)

* **Feature**: Updated `github.com/aws/smithy-go` to latest version
* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.1 (2021-05-20)

* **Dependency Update**: Updated to the latest SDK module versions

# v1.1.0 (2021-05-14)

* **Feature**: Constant has been added to modules to enable runtime version inspection for reporting.
* **Dependency Update**: Updated to the latest SDK module versions

