// Code generated by smithy-go-codegen DO NOT EDIT.

package types

import (
	"fmt"
	smithy "github.com/aws/smithy-go"
)

// You do not have sufficient access to perform this action.
type AccessDeniedException struct {
	Message *string

	ErrorCodeOverride *string

	Error_            *string
	Error_description *string

	noSmithyDocumentSerde
}

func (e *AccessDeniedException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *AccessDeniedException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *AccessDeniedException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "AccessDeniedException"
	}
	return *e.ErrorCodeOverride
}
func (e *AccessDeniedException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Indicates that a request to authorize a client with an access user session
// token is pending.
type AuthorizationPendingException struct {
	Message *string

	ErrorCodeOverride *string

	Error_            *string
	Error_description *string

	noSmithyDocumentSerde
}

func (e *AuthorizationPendingException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *AuthorizationPendingException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *AuthorizationPendingException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "AuthorizationPendingException"
	}
	return *e.ErrorCodeOverride
}
func (e *AuthorizationPendingException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Indicates that the token issued by the service is expired and is no longer
// valid.
type ExpiredTokenException struct {
	Message *string

	ErrorCodeOverride *string

	Error_            *string
	Error_description *string

	noSmithyDocumentSerde
}

func (e *ExpiredTokenException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ExpiredTokenException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ExpiredTokenException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "ExpiredTokenException"
	}
	return *e.ErrorCodeOverride
}
func (e *ExpiredTokenException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Indicates that an error from the service occurred while trying to process a
// request.
type InternalServerException struct {
	Message *string

	ErrorCodeOverride *string

	Error_            *string
	Error_description *string

	noSmithyDocumentSerde
}

func (e *InternalServerException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InternalServerException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InternalServerException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "InternalServerException"
	}
	return *e.ErrorCodeOverride
}
func (e *InternalServerException) ErrorFault() smithy.ErrorFault { return smithy.FaultServer }

// Indicates that the clientId or clientSecret in the request is invalid. For
// example, this can occur when a client sends an incorrect clientId or an expired
// clientSecret .
type InvalidClientException struct {
	Message *string

	ErrorCodeOverride *string

	Error_            *string
	Error_description *string

	noSmithyDocumentSerde
}

func (e *InvalidClientException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidClientException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidClientException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "InvalidClientException"
	}
	return *e.ErrorCodeOverride
}
func (e *InvalidClientException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Indicates that the client information sent in the request during registration
// is invalid.
type InvalidClientMetadataException struct {
	Message *string

	ErrorCodeOverride *string

	Error_            *string
	Error_description *string

	noSmithyDocumentSerde
}

func (e *InvalidClientMetadataException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidClientMetadataException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidClientMetadataException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "InvalidClientMetadataException"
	}
	return *e.ErrorCodeOverride
}
func (e *InvalidClientMetadataException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Indicates that a request contains an invalid grant. This can occur if a client
// makes a CreateTokenrequest with an invalid grant type.
type InvalidGrantException struct {
	Message *string

	ErrorCodeOverride *string

	Error_            *string
	Error_description *string

	noSmithyDocumentSerde
}

func (e *InvalidGrantException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidGrantException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidGrantException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "InvalidGrantException"
	}
	return *e.ErrorCodeOverride
}
func (e *InvalidGrantException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Indicates that one or more redirect URI in the request is not supported for
// this operation.
type InvalidRedirectUriException struct {
	Message *string

	ErrorCodeOverride *string

	Error_            *string
	Error_description *string

	noSmithyDocumentSerde
}

func (e *InvalidRedirectUriException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidRedirectUriException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidRedirectUriException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "InvalidRedirectUriException"
	}
	return *e.ErrorCodeOverride
}
func (e *InvalidRedirectUriException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Indicates that something is wrong with the input to the request. For example, a
// required parameter might be missing or out of range.
type InvalidRequestException struct {
	Message *string

	ErrorCodeOverride *string

	Error_            *string
	Error_description *string

	noSmithyDocumentSerde
}

func (e *InvalidRequestException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidRequestException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidRequestException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "InvalidRequestException"
	}
	return *e.ErrorCodeOverride
}
func (e *InvalidRequestException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Indicates that a token provided as input to the request was issued by and is
// only usable by calling IAM Identity Center endpoints in another region.
type InvalidRequestRegionException struct {
	Message *string

	ErrorCodeOverride *string

	Error_            *string
	Error_description *string
	Endpoint          *string
	Region            *string

	noSmithyDocumentSerde
}

func (e *InvalidRequestRegionException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidRequestRegionException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidRequestRegionException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "InvalidRequestRegionException"
	}
	return *e.ErrorCodeOverride
}
func (e *InvalidRequestRegionException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Indicates that the scope provided in the request is invalid.
type InvalidScopeException struct {
	Message *string

	ErrorCodeOverride *string

	Error_            *string
	Error_description *string

	noSmithyDocumentSerde
}

func (e *InvalidScopeException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidScopeException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidScopeException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "InvalidScopeException"
	}
	return *e.ErrorCodeOverride
}
func (e *InvalidScopeException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Indicates that the client is making the request too frequently and is more than
// the service can handle.
type SlowDownException struct {
	Message *string

	ErrorCodeOverride *string

	Error_            *string
	Error_description *string

	noSmithyDocumentSerde
}

func (e *SlowDownException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *SlowDownException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *SlowDownException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "SlowDownException"
	}
	return *e.ErrorCodeOverride
}
func (e *SlowDownException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Indicates that the client is not currently authorized to make the request. This
// can happen when a clientId is not issued for a public client.
type UnauthorizedClientException struct {
	Message *string

	ErrorCodeOverride *string

	Error_            *string
	Error_description *string

	noSmithyDocumentSerde
}

func (e *UnauthorizedClientException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *UnauthorizedClientException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *UnauthorizedClientException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "UnauthorizedClientException"
	}
	return *e.ErrorCodeOverride
}
func (e *UnauthorizedClientException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Indicates that the grant type in the request is not supported by the service.
type UnsupportedGrantTypeException struct {
	Message *string

	ErrorCodeOverride *string

	Error_            *string
	Error_description *string

	noSmithyDocumentSerde
}

func (e *UnsupportedGrantTypeException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *UnsupportedGrantTypeException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *UnsupportedGrantTypeException) ErrorCode() string {
	if e == nil || e.ErrorCodeOverride == nil {
		return "UnsupportedGrantTypeException"
	}
	return *e.ErrorCodeOverride
}
func (e *UnsupportedGrantTypeException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }
