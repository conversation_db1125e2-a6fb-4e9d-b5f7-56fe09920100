package ccxtenclave

import (
	"crypto/rsa"
	"encoding/base64"
	"fmt"
)

type SecureResponseDecoder struct {
	privateKey *rsa.PrivateKey
}

// 构造解密器
func NewSecureResponseDecoder(privateKeyPEM []byte) (*SecureResponseDecoder, error) {
	privKey, err := loadPrivateKeyFromString(privateKeyPEM)
	if err != nil {
		return nil, fmt.Errorf("load private key: %w", err)
	}

	return &SecureResponseDecoder{
		privateKey: privKey,
	}, nil
}

func (s *SecureResponseDecoder) Decrypt(keyB64, ivB64, resultB64 string) (string, error) {
	// Step 1: decode base64 (RSA 加密的密文)
	keyCipher, err := base64.StdEncoding.DecodeString(keyB64)
	if err != nil {
		return "", fmt.Errorf("decode aes key (rsa encrypted): %w", err)
	}

	ivCipher, err := base64.StdEncoding.DecodeString(ivB64)
	if err != nil {
		return "", fmt.Errorf("decode aes iv (rsa encrypted): %w", err)
	}

	//// Step 2: RSA 解密，直接得到 AES 密钥和 IV 原始字节
	aesKeyDecryptB64, err := rsaDecrypt(s.privateKey, keyCipher)
	if err != nil {
		return "", fmt.Errorf("rsa decrypt aes key: %w", err)
	}

	aesKey, err := base64.URLEncoding.DecodeString(string(aesKeyDecryptB64))
	if err != nil {
		return "", fmt.Errorf("rsa decrypt to base64 get aes key: %w", err)
	}

	ivDecryptB64, err := rsaDecrypt(s.privateKey, ivCipher)
	if err != nil {
		return "", fmt.Errorf("rsa decrypt aes iv: %w", err)
	}

	iv, err := base64.URLEncoding.DecodeString(string(ivDecryptB64))
	if err != nil {
		return "", fmt.Errorf("rsa decrypt to base64 get aes key: %w", err)
	}

	//// Step 3: AES 解密业务密文
	aes, err := NewAesEncrypt(aesKey, iv)
	if err != nil {
		return "", fmt.Errorf("init aes: %w", err)
	}

	result, err := aes.DecryptBase64(resultB64)
	if err != nil {
		return "", fmt.Errorf("aes decrypt result: %w", err)
	}

	return result, nil
}
