package ccxtenclave

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"errors"
	"fmt"
)

func rsaEncrypt(publicKey *rsa.PublicKey, plaintext []byte) ([]byte, error) {
	ciphertext, err := rsa.EncryptPKCS1v15(rand.Reader, publicKey, plaintext)
	if err != nil {
		return nil, err
	}

	return ciphertext, nil
}

func RsaEncryptParamsToBase64(publicKeyStr []byte, plaintext []byte) (string, error) {
	publicKey, err := loadPublicKeyFromString(publicKeyStr)
	if err != nil {
		return "", fmt.Errorf("load public key: %v", err)
	}
	ciphertext, err := rsa.EncryptPKCS1v15(rand.Reader, publicKey, plaintext)
	if err != nil {
		return "", fmt.Errorf("encrypt: %v", err)
	}

	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

func RsaValidatePublicKeyLength(publicKeyStr []byte, len int) error {

	pub, err := loadPublicKeyFromString(publicKeyStr)
	if err != nil {
		return fmt.Errorf("load public key: %v", err)
	}

	if pub.N.BitLen() < len {
		return fmt.Errorf("length of the public key is less than %v", len)
	}

	return nil

}

func rsaDecrypt(privateKey *rsa.PrivateKey, ciphertext []byte) ([]byte, error) {
	plaintext, err := rsa.DecryptPKCS1v15(rand.Reader, privateKey, ciphertext)
	if err != nil {
		return nil, err
	}

	return plaintext, nil
}

func rsaSign(privateKey *rsa.PrivateKey, message []byte) ([]byte, error) {
	hash := sha256.New()
	hash.Write(message)
	hashedMessage := hash.Sum(nil)

	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hashedMessage)
	if err != nil {
		return nil, err
	}

	return signature, nil
}

func RsaSignParamsToBase64(privateKey []byte, message []byte) (string, error) {

	privatekey_rsa, err := loadPrivateKeyFromString(privateKey)

	if err != nil {
		return "", err
	}

	signature, err := rsaSign(privatekey_rsa, message)

	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(signature), nil

}

func rsaVerify(publicKey *rsa.PublicKey, message []byte, signature []byte) error {
	hash := sha256.New()
	hash.Write(message)
	hashedMessage := hash.Sum(nil)

	err := rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hashedMessage, signature)
	return err
}

func loadPublicKeyFromString(publicKeyBytes []byte) (*rsa.PublicKey, error) {
	block, _ := pem.Decode(publicKeyBytes)
	if block == nil {
		return nil, errors.New("invalid public key: not PEM encoded")
	}

	// 使用 PKCS#1 格式解析
	pub, err := x509.ParsePKCS1PublicKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse PKCS#1 public key: %w", err)
	}

	return pub, nil
}

func loadPrivateKeyFromString(private []byte) (*rsa.PrivateKey, error) {

	block, _ := pem.Decode(private)

	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block containing public key")
	}

	pri, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse DER encoded private key: %v", err)
	}

	return pri, nil

}
