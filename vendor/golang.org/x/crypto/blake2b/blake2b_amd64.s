// Code generated by command: go run blake2b_amd64_asm.go -out ../../blake2b_amd64.s -pkg blake2b. DO NOT EDIT.

//go:build amd64 && gc && !purego

#include "textflag.h"

// func hashBlocksSSE4(h *[8]uint64, c *[2]uint64, flag uint64, blocks []byte)
// Requires: SSE2, SSE4.1, SSSE3
TEXT ·hashBlocksSSE4(SB), NOSPLIT, $288-48
	MOVQ  h+0(FP), AX
	MOVQ  c+8(FP), BX
	MOVQ  flag+16(FP), CX
	MOVQ  blocks_base+24(FP), SI
	MOVQ  blocks_len+32(FP), DI
	MOVQ  SP, R10
	ADDQ  $0x0f, R10
	ANDQ  $-16, R10
	MOVOU ·iv3<>+0(SB), X0
	MOVO  X0, (R10)
	XORQ  CX, (R10)
	MOVOU ·c40<>+0(SB), X13
	MOVOU ·c48<>+0(SB), X14
	MOVOU (AX), X12
	MOVOU 16(AX), X15
	MOVQ  (BX), R8
	MOVQ  8(BX), R9

loop:
	ADDQ $0x80, R8
	CMPQ R8, $0x80
	JGE  noinc
	INCQ R9

noinc:
	MOVQ       R8, X8
	PINSRQ     $0x01, R9, X8
	MOVO       X12, X0
	MOVO       X15, X1
	MOVOU      32(AX), X2
	MOVOU      48(AX), X3
	MOVOU      ·iv0<>+0(SB), X4
	MOVOU      ·iv1<>+0(SB), X5
	MOVOU      ·iv2<>+0(SB), X6
	PXOR       X8, X6
	MOVO       (R10), X7
	MOVQ       (SI), X8
	PINSRQ     $0x01, 16(SI), X8
	MOVQ       32(SI), X9
	PINSRQ     $0x01, 48(SI), X9
	MOVQ       8(SI), X10
	PINSRQ     $0x01, 24(SI), X10
	MOVQ       40(SI), X11
	PINSRQ     $0x01, 56(SI), X11
	MOVO       X8, 16(R10)
	MOVO       X9, 32(R10)
	MOVO       X10, 48(R10)
	MOVO       X11, 64(R10)
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X6, X8
	PUNPCKLQDQ X6, X9
	PUNPCKHQDQ X7, X6
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X7, X9
	MOVO       X8, X7
	MOVO       X2, X8
	PUNPCKHQDQ X9, X7
	PUNPCKLQDQ X3, X9
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X3
	MOVQ       64(SI), X8
	PINSRQ     $0x01, 80(SI), X8
	MOVQ       96(SI), X9
	PINSRQ     $0x01, 112(SI), X9
	MOVQ       72(SI), X10
	PINSRQ     $0x01, 88(SI), X10
	MOVQ       104(SI), X11
	PINSRQ     $0x01, 120(SI), X11
	MOVO       X8, 80(R10)
	MOVO       X9, 96(R10)
	MOVO       X10, 112(R10)
	MOVO       X11, 128(R10)
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X2, X8
	PUNPCKLQDQ X2, X9
	PUNPCKHQDQ X3, X2
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X3, X9
	MOVO       X8, X3
	MOVO       X6, X8
	PUNPCKHQDQ X9, X3
	PUNPCKLQDQ X7, X9
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X7
	MOVQ       112(SI), X8
	PINSRQ     $0x01, 32(SI), X8
	MOVQ       72(SI), X9
	PINSRQ     $0x01, 104(SI), X9
	MOVQ       80(SI), X10
	PINSRQ     $0x01, 64(SI), X10
	MOVQ       120(SI), X11
	PINSRQ     $0x01, 48(SI), X11
	MOVO       X8, 144(R10)
	MOVO       X9, 160(R10)
	MOVO       X10, 176(R10)
	MOVO       X11, 192(R10)
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X6, X8
	PUNPCKLQDQ X6, X9
	PUNPCKHQDQ X7, X6
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X7, X9
	MOVO       X8, X7
	MOVO       X2, X8
	PUNPCKHQDQ X9, X7
	PUNPCKLQDQ X3, X9
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X3
	MOVQ       8(SI), X8
	PINSRQ     $0x01, (SI), X8
	MOVQ       88(SI), X9
	PINSRQ     $0x01, 40(SI), X9
	MOVQ       96(SI), X10
	PINSRQ     $0x01, 16(SI), X10
	MOVQ       56(SI), X11
	PINSRQ     $0x01, 24(SI), X11
	MOVO       X8, 208(R10)
	MOVO       X9, 224(R10)
	MOVO       X10, 240(R10)
	MOVO       X11, 256(R10)
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X2, X8
	PUNPCKLQDQ X2, X9
	PUNPCKHQDQ X3, X2
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X3, X9
	MOVO       X8, X3
	MOVO       X6, X8
	PUNPCKHQDQ X9, X3
	PUNPCKLQDQ X7, X9
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X7
	MOVQ       88(SI), X8
	PINSRQ     $0x01, 96(SI), X8
	MOVQ       40(SI), X9
	PINSRQ     $0x01, 120(SI), X9
	MOVQ       64(SI), X10
	PINSRQ     $0x01, (SI), X10
	MOVQ       16(SI), X11
	PINSRQ     $0x01, 104(SI), X11
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X6, X8
	PUNPCKLQDQ X6, X9
	PUNPCKHQDQ X7, X6
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X7, X9
	MOVO       X8, X7
	MOVO       X2, X8
	PUNPCKHQDQ X9, X7
	PUNPCKLQDQ X3, X9
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X3
	MOVQ       80(SI), X8
	PINSRQ     $0x01, 24(SI), X8
	MOVQ       56(SI), X9
	PINSRQ     $0x01, 72(SI), X9
	MOVQ       112(SI), X10
	PINSRQ     $0x01, 48(SI), X10
	MOVQ       8(SI), X11
	PINSRQ     $0x01, 32(SI), X11
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X2, X8
	PUNPCKLQDQ X2, X9
	PUNPCKHQDQ X3, X2
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X3, X9
	MOVO       X8, X3
	MOVO       X6, X8
	PUNPCKHQDQ X9, X3
	PUNPCKLQDQ X7, X9
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X7
	MOVQ       56(SI), X8
	PINSRQ     $0x01, 24(SI), X8
	MOVQ       104(SI), X9
	PINSRQ     $0x01, 88(SI), X9
	MOVQ       72(SI), X10
	PINSRQ     $0x01, 8(SI), X10
	MOVQ       96(SI), X11
	PINSRQ     $0x01, 112(SI), X11
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X6, X8
	PUNPCKLQDQ X6, X9
	PUNPCKHQDQ X7, X6
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X7, X9
	MOVO       X8, X7
	MOVO       X2, X8
	PUNPCKHQDQ X9, X7
	PUNPCKLQDQ X3, X9
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X3
	MOVQ       16(SI), X8
	PINSRQ     $0x01, 40(SI), X8
	MOVQ       32(SI), X9
	PINSRQ     $0x01, 120(SI), X9
	MOVQ       48(SI), X10
	PINSRQ     $0x01, 80(SI), X10
	MOVQ       (SI), X11
	PINSRQ     $0x01, 64(SI), X11
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X2, X8
	PUNPCKLQDQ X2, X9
	PUNPCKHQDQ X3, X2
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X3, X9
	MOVO       X8, X3
	MOVO       X6, X8
	PUNPCKHQDQ X9, X3
	PUNPCKLQDQ X7, X9
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X7
	MOVQ       72(SI), X8
	PINSRQ     $0x01, 40(SI), X8
	MOVQ       16(SI), X9
	PINSRQ     $0x01, 80(SI), X9
	MOVQ       (SI), X10
	PINSRQ     $0x01, 56(SI), X10
	MOVQ       32(SI), X11
	PINSRQ     $0x01, 120(SI), X11
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X6, X8
	PUNPCKLQDQ X6, X9
	PUNPCKHQDQ X7, X6
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X7, X9
	MOVO       X8, X7
	MOVO       X2, X8
	PUNPCKHQDQ X9, X7
	PUNPCKLQDQ X3, X9
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X3
	MOVQ       112(SI), X8
	PINSRQ     $0x01, 88(SI), X8
	MOVQ       48(SI), X9
	PINSRQ     $0x01, 24(SI), X9
	MOVQ       8(SI), X10
	PINSRQ     $0x01, 96(SI), X10
	MOVQ       64(SI), X11
	PINSRQ     $0x01, 104(SI), X11
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X2, X8
	PUNPCKLQDQ X2, X9
	PUNPCKHQDQ X3, X2
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X3, X9
	MOVO       X8, X3
	MOVO       X6, X8
	PUNPCKHQDQ X9, X3
	PUNPCKLQDQ X7, X9
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X7
	MOVQ       16(SI), X8
	PINSRQ     $0x01, 48(SI), X8
	MOVQ       (SI), X9
	PINSRQ     $0x01, 64(SI), X9
	MOVQ       96(SI), X10
	PINSRQ     $0x01, 80(SI), X10
	MOVQ       88(SI), X11
	PINSRQ     $0x01, 24(SI), X11
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X6, X8
	PUNPCKLQDQ X6, X9
	PUNPCKHQDQ X7, X6
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X7, X9
	MOVO       X8, X7
	MOVO       X2, X8
	PUNPCKHQDQ X9, X7
	PUNPCKLQDQ X3, X9
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X3
	MOVQ       32(SI), X8
	PINSRQ     $0x01, 56(SI), X8
	MOVQ       120(SI), X9
	PINSRQ     $0x01, 8(SI), X9
	MOVQ       104(SI), X10
	PINSRQ     $0x01, 40(SI), X10
	MOVQ       112(SI), X11
	PINSRQ     $0x01, 72(SI), X11
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X2, X8
	PUNPCKLQDQ X2, X9
	PUNPCKHQDQ X3, X2
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X3, X9
	MOVO       X8, X3
	MOVO       X6, X8
	PUNPCKHQDQ X9, X3
	PUNPCKLQDQ X7, X9
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X7
	MOVQ       96(SI), X8
	PINSRQ     $0x01, 8(SI), X8
	MOVQ       112(SI), X9
	PINSRQ     $0x01, 32(SI), X9
	MOVQ       40(SI), X10
	PINSRQ     $0x01, 120(SI), X10
	MOVQ       104(SI), X11
	PINSRQ     $0x01, 80(SI), X11
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X6, X8
	PUNPCKLQDQ X6, X9
	PUNPCKHQDQ X7, X6
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X7, X9
	MOVO       X8, X7
	MOVO       X2, X8
	PUNPCKHQDQ X9, X7
	PUNPCKLQDQ X3, X9
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X3
	MOVQ       (SI), X8
	PINSRQ     $0x01, 48(SI), X8
	MOVQ       72(SI), X9
	PINSRQ     $0x01, 64(SI), X9
	MOVQ       56(SI), X10
	PINSRQ     $0x01, 24(SI), X10
	MOVQ       16(SI), X11
	PINSRQ     $0x01, 88(SI), X11
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X2, X8
	PUNPCKLQDQ X2, X9
	PUNPCKHQDQ X3, X2
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X3, X9
	MOVO       X8, X3
	MOVO       X6, X8
	PUNPCKHQDQ X9, X3
	PUNPCKLQDQ X7, X9
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X7
	MOVQ       104(SI), X8
	PINSRQ     $0x01, 56(SI), X8
	MOVQ       96(SI), X9
	PINSRQ     $0x01, 24(SI), X9
	MOVQ       88(SI), X10
	PINSRQ     $0x01, 112(SI), X10
	MOVQ       8(SI), X11
	PINSRQ     $0x01, 72(SI), X11
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X6, X8
	PUNPCKLQDQ X6, X9
	PUNPCKHQDQ X7, X6
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X7, X9
	MOVO       X8, X7
	MOVO       X2, X8
	PUNPCKHQDQ X9, X7
	PUNPCKLQDQ X3, X9
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X3
	MOVQ       40(SI), X8
	PINSRQ     $0x01, 120(SI), X8
	MOVQ       64(SI), X9
	PINSRQ     $0x01, 16(SI), X9
	MOVQ       (SI), X10
	PINSRQ     $0x01, 32(SI), X10
	MOVQ       48(SI), X11
	PINSRQ     $0x01, 80(SI), X11
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X2, X8
	PUNPCKLQDQ X2, X9
	PUNPCKHQDQ X3, X2
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X3, X9
	MOVO       X8, X3
	MOVO       X6, X8
	PUNPCKHQDQ X9, X3
	PUNPCKLQDQ X7, X9
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X7
	MOVQ       48(SI), X8
	PINSRQ     $0x01, 112(SI), X8
	MOVQ       88(SI), X9
	PINSRQ     $0x01, (SI), X9
	MOVQ       120(SI), X10
	PINSRQ     $0x01, 72(SI), X10
	MOVQ       24(SI), X11
	PINSRQ     $0x01, 64(SI), X11
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X6, X8
	PUNPCKLQDQ X6, X9
	PUNPCKHQDQ X7, X6
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X7, X9
	MOVO       X8, X7
	MOVO       X2, X8
	PUNPCKHQDQ X9, X7
	PUNPCKLQDQ X3, X9
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X3
	MOVQ       96(SI), X8
	PINSRQ     $0x01, 104(SI), X8
	MOVQ       8(SI), X9
	PINSRQ     $0x01, 80(SI), X9
	MOVQ       16(SI), X10
	PINSRQ     $0x01, 56(SI), X10
	MOVQ       32(SI), X11
	PINSRQ     $0x01, 40(SI), X11
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X2, X8
	PUNPCKLQDQ X2, X9
	PUNPCKHQDQ X3, X2
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X3, X9
	MOVO       X8, X3
	MOVO       X6, X8
	PUNPCKHQDQ X9, X3
	PUNPCKLQDQ X7, X9
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X7
	MOVQ       80(SI), X8
	PINSRQ     $0x01, 64(SI), X8
	MOVQ       56(SI), X9
	PINSRQ     $0x01, 8(SI), X9
	MOVQ       16(SI), X10
	PINSRQ     $0x01, 32(SI), X10
	MOVQ       48(SI), X11
	PINSRQ     $0x01, 40(SI), X11
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X6, X8
	PUNPCKLQDQ X6, X9
	PUNPCKHQDQ X7, X6
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X7, X9
	MOVO       X8, X7
	MOVO       X2, X8
	PUNPCKHQDQ X9, X7
	PUNPCKLQDQ X3, X9
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X3
	MOVQ       120(SI), X8
	PINSRQ     $0x01, 72(SI), X8
	MOVQ       24(SI), X9
	PINSRQ     $0x01, 104(SI), X9
	MOVQ       88(SI), X10
	PINSRQ     $0x01, 112(SI), X10
	MOVQ       96(SI), X11
	PINSRQ     $0x01, (SI), X11
	PADDQ      X8, X0
	PADDQ      X9, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      X10, X0
	PADDQ      X11, X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X2, X8
	PUNPCKLQDQ X2, X9
	PUNPCKHQDQ X3, X2
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X3, X9
	MOVO       X8, X3
	MOVO       X6, X8
	PUNPCKHQDQ X9, X3
	PUNPCKLQDQ X7, X9
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X7
	PADDQ      16(R10), X0
	PADDQ      32(R10), X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      48(R10), X0
	PADDQ      64(R10), X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X6, X8
	PUNPCKLQDQ X6, X9
	PUNPCKHQDQ X7, X6
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X7, X9
	MOVO       X8, X7
	MOVO       X2, X8
	PUNPCKHQDQ X9, X7
	PUNPCKLQDQ X3, X9
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X3
	PADDQ      80(R10), X0
	PADDQ      96(R10), X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      112(R10), X0
	PADDQ      128(R10), X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X2, X8
	PUNPCKLQDQ X2, X9
	PUNPCKHQDQ X3, X2
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X3, X9
	MOVO       X8, X3
	MOVO       X6, X8
	PUNPCKHQDQ X9, X3
	PUNPCKLQDQ X7, X9
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X7
	PADDQ      144(R10), X0
	PADDQ      160(R10), X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      176(R10), X0
	PADDQ      192(R10), X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X6, X8
	PUNPCKLQDQ X6, X9
	PUNPCKHQDQ X7, X6
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X7, X9
	MOVO       X8, X7
	MOVO       X2, X8
	PUNPCKHQDQ X9, X7
	PUNPCKLQDQ X3, X9
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X3
	PADDQ      208(R10), X0
	PADDQ      224(R10), X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFD     $0xb1, X6, X6
	PSHUFD     $0xb1, X7, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	PSHUFB     X13, X2
	PSHUFB     X13, X3
	PADDQ      240(R10), X0
	PADDQ      256(R10), X1
	PADDQ      X2, X0
	PADDQ      X3, X1
	PXOR       X0, X6
	PXOR       X1, X7
	PSHUFB     X14, X6
	PSHUFB     X14, X7
	PADDQ      X6, X4
	PADDQ      X7, X5
	PXOR       X4, X2
	PXOR       X5, X3
	MOVOU      X2, X11
	PADDQ      X2, X11
	PSRLQ      $0x3f, X2
	PXOR       X11, X2
	MOVOU      X3, X11
	PADDQ      X3, X11
	PSRLQ      $0x3f, X3
	PXOR       X11, X3
	MOVO       X4, X8
	MOVO       X5, X4
	MOVO       X8, X5
	MOVO       X2, X8
	PUNPCKLQDQ X2, X9
	PUNPCKHQDQ X3, X2
	PUNPCKHQDQ X9, X2
	PUNPCKLQDQ X3, X9
	MOVO       X8, X3
	MOVO       X6, X8
	PUNPCKHQDQ X9, X3
	PUNPCKLQDQ X7, X9
	PUNPCKHQDQ X9, X6
	PUNPCKLQDQ X8, X9
	PUNPCKHQDQ X9, X7
	MOVOU      32(AX), X10
	MOVOU      48(AX), X11
	PXOR       X0, X12
	PXOR       X1, X15
	PXOR       X2, X10
	PXOR       X3, X11
	PXOR       X4, X12
	PXOR       X5, X15
	PXOR       X6, X10
	PXOR       X7, X11
	MOVOU      X10, 32(AX)
	MOVOU      X11, 48(AX)
	LEAQ       128(SI), SI
	SUBQ       $0x80, DI
	JNE        loop
	MOVOU      X12, (AX)
	MOVOU      X15, 16(AX)
	MOVQ       R8, (BX)
	MOVQ       R9, 8(BX)
	RET

DATA ·iv3<>+0(SB)/8, $0x1f83d9abfb41bd6b
DATA ·iv3<>+8(SB)/8, $0x5be0cd19137e2179
GLOBL ·iv3<>(SB), RODATA|NOPTR, $16

DATA ·c40<>+0(SB)/8, $0x0201000706050403
DATA ·c40<>+8(SB)/8, $0x0a09080f0e0d0c0b
GLOBL ·c40<>(SB), RODATA|NOPTR, $16

DATA ·c48<>+0(SB)/8, $0x0100070605040302
DATA ·c48<>+8(SB)/8, $0x09080f0e0d0c0b0a
GLOBL ·c48<>(SB), RODATA|NOPTR, $16

DATA ·iv0<>+0(SB)/8, $0x6a09e667f3bcc908
DATA ·iv0<>+8(SB)/8, $0xbb67ae8584caa73b
GLOBL ·iv0<>(SB), RODATA|NOPTR, $16

DATA ·iv1<>+0(SB)/8, $0x3c6ef372fe94f82b
DATA ·iv1<>+8(SB)/8, $0xa54ff53a5f1d36f1
GLOBL ·iv1<>(SB), RODATA|NOPTR, $16

DATA ·iv2<>+0(SB)/8, $0x510e527fade682d1
DATA ·iv2<>+8(SB)/8, $0x9b05688c2b3e6c1f
GLOBL ·iv2<>(SB), RODATA|NOPTR, $16
