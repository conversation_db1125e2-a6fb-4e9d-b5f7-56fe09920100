package log

type Config struct {
	FilePath          string   `json:"filePath"`
	FileLink          string   `json:"fileLink"`
	DeleteFileOnExit  bool     `json:"deleteFileOnExit"`
	IgnoreUriAll      []string `json:"ignoreUri"`
	IgnoreUriParams   []string `json:"ignoreUriParams"`
	IgnoreUriResponse []string `json:"ignoreUriResponse"`
}

func SetConfig(c Config) {
	config = c
}

func GetConfig() Config {
	return config
}

func SetfilePath(filePath string) {
	config.FilePath = filePath
}
