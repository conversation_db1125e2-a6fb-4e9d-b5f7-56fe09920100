debug: true
serveAddr: ":8080"
pprofPort: ":9090"
sso:
  url: "https://sec-sso.yorkapp.com"
  endPoint: "https://sec-sso.yorkapp.com"
  clientID: "39851b8ce503212b780f"
  clientSecret: "6c406c8c262c7a3b297f3af60eb8b39bed03c947"
  cert: "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"
  orgName: "Avenir"
  appName: "AAS-Management"
  enforcerID: ""
  apiHost: "http://localhost:8080"
  loginPath: "/sso/login"
  logoutPath: "/sso/logout"
  callbackPath: "/sso/callback"
  homepage: "http://localhost:8085"
  denypage: "http://localhost:8085/noauth"
  noValidateSSOPath: [ "/healthy","/list/config/sync"]
  noValidateIAMPath: [ "/healthy","/sso/*","/list/config/sync" ]
cookie:
  #  // AES only supports key sizes of 16, 24 or 32 bytes.
  #  // You either need to provide exactly that amount or you derive the key from what you type in.
  blockKey: "00e058f58a2ee0dec991e18531eb31c1"
  hashKey: "469b2b3c73758905bd57d0a3e551775111949fa2bfc805cc0c2e1127f4f0a6e9"
  expireSeconds: 86400
  nameAccount: "avenir_aas_management_account"
  nameAccessToken: "avenir_aas_management_access_token"
  nameClaims: "avenir_aas_management_claims"
  nameServer: "_avenir_aas_server"
mysql:
  dsn: "root:123456@tcp(127.0.0.1:3306)/sec_aas_secret?charset=utf8&timeout=100ms&parseTime=True&loc=Local"
redis:
  host: "127.0.0.1"
  port: 6379
  password: "123456"
  db: 0
internalApi:
  sourceKey: "management-api"
  endpoint: "http://127.0.0.1:8082"
  publicKey: "LS0tLS1CRUdJTiBSU0EgUFVCTElDIEtFWS0tLS0tCk1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBMFNCNmJCUlNQQU5nNVd5Y05MWVYKcENTcElCQVA1Z3pIejFHeUNQZnBBMW5rQmlMRmxvcWt1TTRYeTdPK2VXL1dveHNVYlA3M2hNVUViQW1hU09XaAplVEl1bzRPZ1hPYVF2dGcyNk1oN1pHV1EzL2hQV0tXK1JHUjJtTjBXdW1kYWVMdWIwT1J0STR6UFRZdUhnZU11CkdPcXdvUFBFS2dGWHRmOTRuUU5sdy9aMlJGblMyL1Zva3RaRHVjeGVUQ2dURlFnVnJkaW5DNGdRK0NiQjE1cSsKeTBkQ0o4L0p2SnlRZ25QWFFIVS90d2hnTEZxUXZ0NlArbHdTMG9HK1B2bEVJdW1ud1YxSGNzYWYvS1ljQVpTcgozdzhsZDUrMGlvcithWUZwRVlENmY2bVJoRWE1RWdUUnJha3RpN1lPenZadGNsck9QVEUrRW5IVng5RnB1anRaCk13SURBUUFCCi0tLS0tRU5EIFJTQSBQVUJMSUMgS0VZLS0tLS0K"
encrypter:
  publicKey: "LS0tLS1CRUdJTiBSU0EgUFVCTElDIEtFWS0tLS0tCk1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBclgycjFScUYydndlT01jUThpS0wKaG9NZmlPWTZSNW5TV3dDZTJ0SUFEY2doWlN3bDVSNm1JRnVtM0FBNlhkcTZJSm9CbnBkZ0JqNnAxV25POXFaZAo2S1BQMUJ6blA2SkFmamZRQitiM0czY1g2MjdJZERZNmZnSUN2N01KMm9rYzNkalVpUmtSeEF6WFBCM1VuWVBFCnRHb3N2bjh6MkY3UGJwRzRNWWZqM0FhQzE1L0Ntejl0YURaV3FVaERtRjNSUFhkRU5rVlBJT254K3NHZ2lLckcKZ2NFODR6SW93bFkrbU42RFhXbzg4UTA2M3dwcFdWaWR1RmkrRDhKK1V3UExFbDJPc0VnQzVoK3hJMEZwNWtlMAorVldyTmZmK3h0WlNaNksrbGh3Mys3NitFZ2pYSnVnTmJuWjFkcStldWxNWkx2NEVKaGpmTUVua1R4dnZFWWVUCndRSURBUUFCCi0tLS0tRU5EIFJTQSBQVUJMSUMgS0VZLS0tLS0K"
front:
  aesKey: "GOqwoPPEKgFXtf94nQNlwQo3FpMZVXEV"
log:
  filePath: "logs/management-api-%Y%m%d.log"
  fileLink: "logs/management-api.log"
  deleteFileOnExit: false
  ignoreUriAll: ["/healthy","/management/app_credential/add","/management/internal_credential/add",]
  ignoreUriParams: ["/healthy","/management/securities_secret/add","/management/crypto_secret/add"]
  ignoreUriResponse: ["/management/internal_credential/download"]
ssl:
  enabled: true
  port: 5443
  certFile: "conf/sec-test.yorkapp.com.pem"
  keyFile: "conf/sec-test.yorkapp.com.key"
socApi:
  endpoint: "https://sec-soc-api.yorkapp.com"
  ipsPath: "/open-api/ips/pub-ip"
  ec2Path: "/open-api/ec2/pub-ip"
exchangesecret:
  endpoint: "https://exchange-signer.sec-test.yorkapp.com"
  publicKey: "LS0tLS1CRUdJTiBSU0EgUFVCTElDIEtFWS0tLS0tCk1JSUJDZ0tDQVFFQTQwdHZDcnRZVkVQZTVmOHA5TVJKUGZHdWZqeGdTNURmSXY0QWVURXdBb203V0hsSS9GeTIKRWYxdUJIVEd1dUYyL1RRc2JDMnEzdVcrWWhWejQ1b2ozUGY2TXZGei9CODRFRElLQlNaTTZ0VGhoK0VZQXViOQp0eCtpN1dTbHdISjBKSGViVk4rRlBJdTVKYmV5Sy80UFowNXlWYVZzYWZDRWxqb2xQK1c0Z2kzU2JXUWdobTFmClhQOEFGT0dTaVcxUkxZWDJ3Q1p5MnZrbHkyS0dSNCtLd2drK1JKRFRwek5TSEgrNzh3ejFqWFZSbC9RaCswQVYKczBQK3RSazI2Q0lMVnhiaFVncnVTamdROGkwOGlzbTVpMEpMVHhWL0I5Y3Y5VTZjWWJmcEJMbHlxcXBnOXJZeQozdHlsSFFwQWFkRjhKUEVjSVE1SVJTN2Q3N1lINzZpQ3d3SURBUUFCCi0tLS0tRU5EIFJTQSBQVUJMSUMgS0VZLS0tLS0K"
  sourceKey: "aas"
  privateKey: "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"
