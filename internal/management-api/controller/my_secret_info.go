package controller

import (
	"fmt"
	"github.com/kataras/iris/v12"
	"gitlab.docsl.com/security/avenir-account-shield/sensitive-server/internal/management-api/common"
	internalConfig "gitlab.docsl.com/security/avenir-account-shield/sensitive-server/internal/management-api/config"
	"gitlab.docsl.com/security/avenir-account-shield/sensitive-server/internal/management-api/logic/secret_access"
	"gitlab.docsl.com/security/avenir-account-shield/sensitive-server/internal/management-api/utils/privateaes"
	pkgCommon "gitlab.docsl.com/security/avenir-account-shield/sensitive-server/pkg/common"
	"gitlab.docsl.com/security/avenir-account-shield/sensitive-server/pkg/config"
	"gitlab.docsl.com/security/common_utils/sensitive"
	"gitlab.docsl.com/security/common_helper/exchangesecret"
)

// MySecretInfoPageParams 定义我的秘密信息分页参数结构
type MySecretInfoPageParams struct {
	Page                      int    `json:"page" validate:"gt=0"`      // 页码
	PageSize                  int    `json:"page_size" validate:"gt=0"` // 每页大小
	Status                    *int32 `json:"status"`                    // 状态
	ConfigTableFieldUniqueKey string `json:"config_table_field_unique_key"`
}

// MySecretInfoPage 分页获取当前登录用户的秘密信息列表
func MySecretInfoPage(ctx iris.Context) {
	var p MySecretInfoPageParams
	if err := ctx.ReadJSON(&p); err != nil {
		pkgCommon.ResponseErrorWithI8nMessage(ctx, pkgCommon.CodeErrorParamsValidatorError, "", err.Error())
		return
	}

	// 获取当前登录用户信息
	u, err := ctx.User().GetRaw()
	if err != nil {
		pkgCommon.ResponseErrorWithI8nMessage(ctx, pkgCommon.CodeFailNotLogin, "")
		return
	}
	userAccount := u.(common.User).Account

	// 初始化服务
	l := secret_access.NewSecretAccess(ctx)

	// 计算分页偏移量
	offset := p.PageSize * (p.Page - 1)

	// 获取用户秘密信息分页数据
	res, total, err := l.GetSecretInfoUserDetailPage(0, userAccount, p.ConfigTableFieldUniqueKey, p.Status, p.PageSize, offset)
	if err != nil {
		common.ResponseErrorWithI8nMessage(ctx, common.CodeErrorGetData, err.Error())
		return
	}

	pkgCommon.ResponseSuccess(ctx, map[string]interface{}{
		"total": total,
		"list":  res,
	})
	return
}

// MySecretInfoGetAllParams 定义获取所有秘密信息的参数结构
type MySecretInfoGetAllParams struct {
	Status                    *int32 `json:"status"` // 状态
	ConfigTableFieldUniqueKey string `json:"config_table_field_unique_key"`
}

// MySecretInfoGetAll 获取当前登录用户的所有秘密信息列表
func MySecretInfoGetAll(ctx iris.Context) {
	var p MySecretInfoGetAllParams
	if err := ctx.ReadJSON(&p); err != nil {
		pkgCommon.ResponseErrorWithI8nMessage(ctx, pkgCommon.CodeErrorParamsValidatorError, "", err.Error())
		return
	}

	// 获取当前登录用户信息
	u, err := ctx.User().GetRaw()
	if err != nil {
		pkgCommon.ResponseErrorWithI8nMessage(ctx, pkgCommon.CodeFailNotLogin, "")
		return
	}
	userAccount := u.(common.User).Account

	// 初始化服务
	l := secret_access.NewSecretAccess(ctx)

	// 获取用户所有秘密信息
	res, err := l.GetAllSecretInfoUserDetail(userAccount, p.ConfigTableFieldUniqueKey, p.Status)
	if err != nil {
		common.ResponseErrorWithI8nMessage(ctx, common.CodeErrorGetData, err.Error())
		return
	}

	pkgCommon.ResponseSuccess(ctx, map[string]interface{}{
		"total": len(res),
		"list":  res,
	})
	return
}

// MySecretInfoGet 获取当前登录用户的GA
func MySecretInfoGet(ctx iris.Context) {
	var p IDParams
	if err := ctx.ReadJSON(&p); err != nil {
		pkgCommon.ResponseErrorWithI8nMessage(ctx, pkgCommon.CodeErrorParamsValidatorError, "", err.Error())
		return
	}

	// 获取当前登录用户信息
	u, err := ctx.User().GetRaw()
	if err != nil {
		pkgCommon.ResponseErrorWithI8nMessage(ctx, pkgCommon.CodeFailNotLogin, "")
		return
	}
	userAccount := u.(common.User).Account

	// 初始化服务
	l := secret_access.NewSecretAccess(ctx)

	// 获取用户秘密信息
	res, err := l.GetSecretInfoUserDetailByIDAndUser(p.ID, userAccount)
	if err != nil {
		common.ResponseErrorWithI8nMessage(ctx, common.CodeErrorGetData, err.Error())
		return
	}

	pkgCommon.ResponseSuccess(ctx, res)
	return
}

type MySecretInfoViewParams struct {
	ID int64 `json:"id" validate:"required"`
	//SecretInfoId    int64  `json:"secret_info_id" validate:"required"`
	//UserPublicKeyId int64  `json:"user_public_key_id" validate:"required"`
	SecretDecryptedEncrypt string `json:"secret_decrypted_encrypt" validate:"required"`
	Exchange               string `json:"exchange" validate:"required"`
}

// MySecretInfoViewGA 获取当前登录用户的GA
func MySecretInfoViewGA(ctx iris.Context) {
	var p MySecretInfoViewParams
	if err := ctx.ReadJSON(&p); err != nil {
		pkgCommon.ResponseErrorWithI8nMessage(ctx, pkgCommon.CodeErrorParamsValidatorError, "", err.Error())
		return
	}

	// 获取当前登录用户信息
	u, err := ctx.User().GetRaw()
	if err != nil {
		pkgCommon.ResponseErrorWithI8nMessage(ctx, pkgCommon.CodeFailNotLogin, "")
		return
	}
	userAccount := u.(common.User).Account

	// 初始化服务
	l := secret_access.NewSecretAccess(ctx)

	// 获取用户秘密信息
	res, err := l.GetSecretInfoUserDetailByIDAndUser(p.ID, userAccount)
	if err != nil {
		common.ResponseErrorWithI8nMessage(ctx, common.CodeErrorGetData, err.Error())
		return
	}
	publicKey, err := l.GetUserPublicKeyByID(res.UserPublicKeyID)
	if err != nil {
		pkgCommon.ResponseErrorWithI8nMessage(ctx, pkgCommon.CodeErrorGetData, "", err.Error())
		return
	}
	publicKeyOrigin, err := sensitive.Base64Decode(publicKey.KeyText)
	if err != nil {
		pkgCommon.ResponseErrorWithI8nMessage(ctx, pkgCommon.CodeErrorBase64Decode, "", err.Error())
		return
	}
	originValue, err := privateaes.Decrypt(p.SecretDecryptedEncrypt, []byte(config.C.(*internalConfig.ManagementApi).Front.AesKey))

	secretInfo, err := l.GetSecretInfoByID(res.SecretInfoID)
	if err != nil {
		pkgCommon.ResponseErrorWithI8nMessage(ctx, pkgCommon.CodeErrorGetData, "", err.Error())
		return
	}
	kms, err := l.GetAwsKmsKeyIDByID(secretInfo.KmsKeyID)
	if err != nil {
		pkgCommon.ResponseErrorWithI8nMessage(ctx, pkgCommon.CodeErrorGetData, "", err.Error())
		return
	}
	fmt.Println(publicKeyOrigin, originValue, kms)
	exchangesecret.SetConfig(exchangesecret.Config{
		Endpoint: config.C.(*internalConfig.ManagementApi).Exchangesecret.Endpoint,
		PublicKey: func() string {
			s, err := sensitive.Base64Decode(config.C.(*internalConfig.ManagementApi).Exchangesecret.PublicKey)
			if err != nil {
				return ""
			}
			return s
		}(),
		SourceKey: config.C.(*internalConfig.ManagementApi).Exchangesecret.SourceKey,
	client :=exchangesecret.NewSecretClient(ctx)

	pkgCommon.ResponseSuccess(ctx, "")
	return
}

// MySecretInfoViewPassword 获取当前登录用户的GA
func MySecretInfoViewPassword(ctx iris.Context) {
	var p MySecretInfoGetAllParams
	if err := ctx.ReadJSON(&p); err != nil {
		pkgCommon.ResponseErrorWithI8nMessage(ctx, pkgCommon.CodeErrorParamsValidatorError, "", err.Error())
		return
	}

	// 获取当前登录用户信息
	u, err := ctx.User().GetRaw()
	if err != nil {
		pkgCommon.ResponseErrorWithI8nMessage(ctx, pkgCommon.CodeFailNotLogin, "")
		return
	}
	userAccount := u.(common.User).Account

	// 初始化服务
	l := secret_access.NewSecretAccess(ctx)

	// 获取用户所有秘密信息
	res, err := l.GetAllSecretInfoUserDetail(userAccount, p.ConfigTableFieldUniqueKey, p.Status)
	if err != nil {
		common.ResponseErrorWithI8nMessage(ctx, common.CodeErrorGetData, err.Error())
		return
	}

	pkgCommon.ResponseSuccess(ctx, map[string]interface{}{
		"total": len(res),
		"list":  res,
	})
	return
}
