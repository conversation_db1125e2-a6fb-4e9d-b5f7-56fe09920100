// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.docsl.com/security/avenir-account-shield/sensitive-server/internal/management-api/dao/model"
)

func newSettingAwsKmsKeyid(db *gorm.DB, opts ...gen.DOOption) settingAwsKmsKeyid {
	_settingAwsKmsKeyid := settingAwsKmsKeyid{}

	_settingAwsKmsKeyid.settingAwsKmsKeyidDo.UseDB(db, opts...)
	_settingAwsKmsKeyid.settingAwsKmsKeyidDo.UseModel(&model.SettingAwsKmsKeyid{})

	tableName := _settingAwsKmsKeyid.settingAwsKmsKeyidDo.TableName()
	_settingAwsKmsKeyid.ALL = field.NewAsterisk(tableName)
	_settingAwsKmsKeyid.ID = field.NewInt64(tableName, "id")
	_settingAwsKmsKeyid.Name = field.NewString(tableName, "name")
	_settingAwsKmsKeyid.AwsKeyAccount = field.NewString(tableName, "aws_key_account")
	_settingAwsKmsKeyid.AwsKeyID = field.NewString(tableName, "aws_key_id")
	_settingAwsKmsKeyid.KeyType = field.NewInt32(tableName, "key_type")
	_settingAwsKmsKeyid.AlgorithmType = field.NewInt32(tableName, "algorithm_type")
	_settingAwsKmsKeyid.Status = field.NewInt32(tableName, "status")
	_settingAwsKmsKeyid.Tenant = field.NewString(tableName, "tenant")
	_settingAwsKmsKeyid.Creator = field.NewString(tableName, "creator")
	_settingAwsKmsKeyid.CreatedAt = field.NewTime(tableName, "created_at")
	_settingAwsKmsKeyid.UpdatedAt = field.NewTime(tableName, "updated_at")
	_settingAwsKmsKeyid.DeletedAt = field.NewField(tableName, "deleted_at")

	_settingAwsKmsKeyid.fillFieldMap()

	return _settingAwsKmsKeyid
}

// settingAwsKmsKeyid aws的kms keyid的加密算法的配置
type settingAwsKmsKeyid struct {
	settingAwsKmsKeyidDo settingAwsKmsKeyidDo

	ALL           field.Asterisk
	ID            field.Int64
	Name          field.String // 名称
	AwsKeyAccount field.String // aws key 所属的账号
	AwsKeyID      field.String // aws key id
	KeyType       field.Int32  // key的类型
	AlgorithmType field.Int32  // 算法的类型
	Status        field.Int32  // 状态0未启用 1启用
	Tenant        field.String // 租户
	Creator       field.String // 创建人
	CreatedAt     field.Time   // 数据创建时间
	UpdatedAt     field.Time   // 数据更新时间
	DeletedAt     field.Field  // 数据逻辑删除时间

	fieldMap map[string]field.Expr
}

func (s settingAwsKmsKeyid) Table(newTableName string) *settingAwsKmsKeyid {
	s.settingAwsKmsKeyidDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s settingAwsKmsKeyid) As(alias string) *settingAwsKmsKeyid {
	s.settingAwsKmsKeyidDo.DO = *(s.settingAwsKmsKeyidDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *settingAwsKmsKeyid) updateTableName(table string) *settingAwsKmsKeyid {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.Name = field.NewString(table, "name")
	s.AwsKeyAccount = field.NewString(table, "aws_key_account")
	s.AwsKeyID = field.NewString(table, "aws_key_id")
	s.KeyType = field.NewInt32(table, "key_type")
	s.AlgorithmType = field.NewInt32(table, "algorithm_type")
	s.Status = field.NewInt32(table, "status")
	s.Tenant = field.NewString(table, "tenant")
	s.Creator = field.NewString(table, "creator")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.DeletedAt = field.NewField(table, "deleted_at")

	s.fillFieldMap()

	return s
}

func (s *settingAwsKmsKeyid) WithContext(ctx context.Context) *settingAwsKmsKeyidDo {
	return s.settingAwsKmsKeyidDo.WithContext(ctx)
}

func (s settingAwsKmsKeyid) TableName() string { return s.settingAwsKmsKeyidDo.TableName() }

func (s settingAwsKmsKeyid) Alias() string { return s.settingAwsKmsKeyidDo.Alias() }

func (s settingAwsKmsKeyid) Columns(cols ...field.Expr) gen.Columns {
	return s.settingAwsKmsKeyidDo.Columns(cols...)
}

func (s *settingAwsKmsKeyid) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *settingAwsKmsKeyid) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 12)
	s.fieldMap["id"] = s.ID
	s.fieldMap["name"] = s.Name
	s.fieldMap["aws_key_account"] = s.AwsKeyAccount
	s.fieldMap["aws_key_id"] = s.AwsKeyID
	s.fieldMap["key_type"] = s.KeyType
	s.fieldMap["algorithm_type"] = s.AlgorithmType
	s.fieldMap["status"] = s.Status
	s.fieldMap["tenant"] = s.Tenant
	s.fieldMap["creator"] = s.Creator
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["deleted_at"] = s.DeletedAt
}

func (s settingAwsKmsKeyid) clone(db *gorm.DB) settingAwsKmsKeyid {
	s.settingAwsKmsKeyidDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s settingAwsKmsKeyid) replaceDB(db *gorm.DB) settingAwsKmsKeyid {
	s.settingAwsKmsKeyidDo.ReplaceDB(db)
	return s
}

type settingAwsKmsKeyidDo struct{ gen.DO }

func (s settingAwsKmsKeyidDo) Debug() *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Debug())
}

func (s settingAwsKmsKeyidDo) WithContext(ctx context.Context) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s settingAwsKmsKeyidDo) ReadDB() *settingAwsKmsKeyidDo {
	return s.Clauses(dbresolver.Read)
}

func (s settingAwsKmsKeyidDo) WriteDB() *settingAwsKmsKeyidDo {
	return s.Clauses(dbresolver.Write)
}

func (s settingAwsKmsKeyidDo) Session(config *gorm.Session) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Session(config))
}

func (s settingAwsKmsKeyidDo) Clauses(conds ...clause.Expression) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s settingAwsKmsKeyidDo) Returning(value interface{}, columns ...string) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s settingAwsKmsKeyidDo) Not(conds ...gen.Condition) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s settingAwsKmsKeyidDo) Or(conds ...gen.Condition) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s settingAwsKmsKeyidDo) Select(conds ...field.Expr) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s settingAwsKmsKeyidDo) Where(conds ...gen.Condition) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s settingAwsKmsKeyidDo) Order(conds ...field.Expr) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s settingAwsKmsKeyidDo) Distinct(cols ...field.Expr) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s settingAwsKmsKeyidDo) Omit(cols ...field.Expr) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s settingAwsKmsKeyidDo) Join(table schema.Tabler, on ...field.Expr) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s settingAwsKmsKeyidDo) LeftJoin(table schema.Tabler, on ...field.Expr) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s settingAwsKmsKeyidDo) RightJoin(table schema.Tabler, on ...field.Expr) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s settingAwsKmsKeyidDo) Group(cols ...field.Expr) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s settingAwsKmsKeyidDo) Having(conds ...gen.Condition) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s settingAwsKmsKeyidDo) Limit(limit int) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s settingAwsKmsKeyidDo) Offset(offset int) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s settingAwsKmsKeyidDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s settingAwsKmsKeyidDo) Unscoped() *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Unscoped())
}

func (s settingAwsKmsKeyidDo) Create(values ...*model.SettingAwsKmsKeyid) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s settingAwsKmsKeyidDo) CreateInBatches(values []*model.SettingAwsKmsKeyid, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s settingAwsKmsKeyidDo) Save(values ...*model.SettingAwsKmsKeyid) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s settingAwsKmsKeyidDo) First() (*model.SettingAwsKmsKeyid, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SettingAwsKmsKeyid), nil
	}
}

func (s settingAwsKmsKeyidDo) Take() (*model.SettingAwsKmsKeyid, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SettingAwsKmsKeyid), nil
	}
}

func (s settingAwsKmsKeyidDo) Last() (*model.SettingAwsKmsKeyid, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SettingAwsKmsKeyid), nil
	}
}

func (s settingAwsKmsKeyidDo) Find() ([]*model.SettingAwsKmsKeyid, error) {
	result, err := s.DO.Find()
	return result.([]*model.SettingAwsKmsKeyid), err
}

func (s settingAwsKmsKeyidDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SettingAwsKmsKeyid, err error) {
	buf := make([]*model.SettingAwsKmsKeyid, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s settingAwsKmsKeyidDo) FindInBatches(result *[]*model.SettingAwsKmsKeyid, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s settingAwsKmsKeyidDo) Attrs(attrs ...field.AssignExpr) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s settingAwsKmsKeyidDo) Assign(attrs ...field.AssignExpr) *settingAwsKmsKeyidDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s settingAwsKmsKeyidDo) Joins(fields ...field.RelationField) *settingAwsKmsKeyidDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s settingAwsKmsKeyidDo) Preload(fields ...field.RelationField) *settingAwsKmsKeyidDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s settingAwsKmsKeyidDo) FirstOrInit() (*model.SettingAwsKmsKeyid, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SettingAwsKmsKeyid), nil
	}
}

func (s settingAwsKmsKeyidDo) FirstOrCreate() (*model.SettingAwsKmsKeyid, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SettingAwsKmsKeyid), nil
	}
}

func (s settingAwsKmsKeyidDo) FindByPage(offset int, limit int) (result []*model.SettingAwsKmsKeyid, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s settingAwsKmsKeyidDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s settingAwsKmsKeyidDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s settingAwsKmsKeyidDo) Delete(models ...*model.SettingAwsKmsKeyid) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *settingAwsKmsKeyidDo) withDO(do gen.Dao) *settingAwsKmsKeyidDo {
	s.DO = *do.(*gen.DO)
	return s
}
