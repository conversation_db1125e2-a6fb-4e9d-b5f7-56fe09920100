// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameSettingAwsKmsKeyid = "setting_aws_kms_keyid"

// SettingAwsKmsKeyid aws的kms keyid的加密算法的配置
type SettingAwsKmsKeyid struct {
	ID            int64          `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Name          string         `gorm:"column:name;not null;comment:名称" json:"name"`                                           // 名称
	AwsKeyAccount string         `gorm:"column:aws_key_account;not null;comment:aws key 所属的账号" json:"aws_key_account"`          // aws key 所属的账号
	AwsKeyID      string         `gorm:"column:aws_key_id;not null;comment:aws key id" json:"aws_key_id"`                       // aws key id
	KeyType       int32          `gorm:"column:key_type;not null;comment:key的类型" json:"key_type"`                               // key的类型
	AlgorithmType int32          `gorm:"column:algorithm_type;not null;comment:算法的类型" json:"algorithm_type"`                    // 算法的类型
	Status        int32          `gorm:"column:status;not null;comment:状态0未启用 1启用" json:"status"`                               // 状态0未启用 1启用
	Tenant        string         `gorm:"column:tenant;not null;comment:租户" json:"tenant"`                                       // 租户
	Creator       string         `gorm:"column:creator;not null;comment:创建人" json:"creator"`                                    // 创建人
	CreatedAt     time.Time      `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP;comment:数据创建时间" json:"created_at"` // 数据创建时间
	UpdatedAt     time.Time      `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;comment:数据更新时间" json:"updated_at"` // 数据更新时间
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at;comment:数据逻辑删除时间" json:"deleted_at"`                                  // 数据逻辑删除时间
}

// TableName SettingAwsKmsKeyid's table name
func (*SettingAwsKmsKeyid) TableName() string {
	return TableNameSettingAwsKmsKeyid
}
