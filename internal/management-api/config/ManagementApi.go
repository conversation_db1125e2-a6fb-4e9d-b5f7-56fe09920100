package config

type ManagementApi struct {
	<PERSON>ie         <PERSON>ie         `yaml:"cookie" `
	Debug          bool           `yaml:"debug" `
	Encrypter      Encrypter      `yaml:"encrypter" `
	Exchangesecret Exchangesecret `yaml:"exchangesecret" `
	Front          Front          `yaml:"front" `
	InternalApi    InternalApi    `yaml:"internalApi" `
	Log            Log            `yaml:"log" `
	Mysql          Mysql          `yaml:"mysql" `
	PprofPort      string         `yaml:"pprofPort" `
	Redis          Redis          `yaml:"redis" `
	ServeAddr      string         `yaml:"serveAddr" `
	SocApi         SocApi         `yaml:"socApi" `
	Ssl            Ssl            `yaml:"ssl" `
	Sso            Sso            `yaml:"sso" `
}
